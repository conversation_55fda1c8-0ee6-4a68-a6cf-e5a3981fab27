// Dart
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

// Flutter & 3rd-party
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/views/profil/boutigak/creation_store_page.dart';
import 'package:boutigak/views/profil/boutigak/edit_store_page.dart';
import 'package:boutigak/views/profil/boutigak/my_store_orders_page.dart';
import 'package:boutigak/views/profil/boutigak/mystore_page.dart';
import 'package:boutigak/views/profil/boutigak/payment_provider_page.dart';
import 'package:boutigak/views/profil/boutigak/store_social_media_link_page.dart';
import 'package:boutigak/views/profil/store_order_validation_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
// <-- un seul import, avec alias
import 'package:badges/badges.dart' as badges; 

// App (adapte les chemins à ton projet)
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:url_launcher/url_launcher.dart';



// =======================
//  Abonnement: statut + helpers
// =======================
enum SubStatus { loading, active, inactive }

bool _isNowWithin(DateTime? start, DateTime? end) {
  if (start == null || end == null) return false;
  final now = DateTime.now();
  final endInclusive = DateTime(end.year, end.month, end.day, 23, 59, 59, 999);
  return (now.isAfter(start) || now.isAtSameMomentAs(start)) &&
         (now.isBefore(endInclusive) || now.isAtSameMomentAs(endInclusive));
}

SubStatus _resolveSubscriptionStatusFromAuth(AuthController auth) {
  if (auth.tokenLoading.value) return SubStatus.loading;
  final u = auth.user;
  if (u == null) return SubStatus.loading;

  if (auth.hasSubscription.value == true) {
    if (u.subscriptionsStartDate != null || u.subscriptionsEndDate != null) {
      return _isNowWithin(u.subscriptionsStartDate, u.subscriptionsEndDate)
          ? SubStatus.active
          : SubStatus.inactive;
    }
    return SubStatus.active;
  }

  final bool flag = u.hasSubscription == true;
  if (flag) {
    if (u.subscriptionsStartDate != null || u.subscriptionsEndDate != null) {
      return _isNowWithin(u.subscriptionsStartDate, u.subscriptionsEndDate)
          ? SubStatus.active
          : SubStatus.inactive;
    }
    return SubStatus.active;
  }

  return SubStatus.inactive;
}

// ============================================================
//                       PAGE PRINCIPALE
// ============================================================
class BoutigakUserPage extends StatefulWidget {
  const BoutigakUserPage({super.key});

  @override
  State<BoutigakUserPage> createState() => _BoutigakUserPageState();
}

class _BoutigakUserPageState extends State<BoutigakUserPage> {
  final AuthController authController = Get.find<AuthController>();
  final StoreController storeController =
      Get.isRegistered<StoreController>() ? Get.find<StoreController>() : Get.put(StoreController());

  // Boot/loading contrôlé pour afficher le shimmer au lancement et pendant refresh
  final RxBool _pageBootLoading = true.obs;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _refreshUserAndStore(); // premier fetch -> shimmer garanti
    });
  }

  // ---------- REFRESH centralisé (appelé au boot et au retour des pages) ----------
  Future<void> _refreshUserAndStore() async {
    try {
      _pageBootLoading.value = true; // start shimmer
      await authController.getAndSaveUser();
      if (authController.user?.hasStore == true) {
        await storeController.fetchedMyStoreInformation();
      } else {
        storeController.myStore.value = null; // état propre si pas de store
      }
    } catch (e) {
      debugPrint('refresh error: $e');
    } finally {
      _pageBootLoading.value = false; // stop shimmer
    }
  }
  // -------------------------------------------------------------------------------

 
  @override
  Widget build(BuildContext context) {
    final theme  = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Couleurs shimmer (inchangées)
    final shimmerBase = isDark ? Colors.white.withOpacity(0.10) : Colors.grey.shade300;
    final shimmerHigh = isDark ? Colors.white.withOpacity(0.25) : Colors.grey.shade100;
    final tileStroke  = theme.dividerColor.withOpacity(0.20);

    return Obx(() {
      final store = storeController.myStore.value;
      final storeName = (store?.name?.isNotEmpty ?? false) ? store!.name! : "Boutigak";
      final subActive = _resolveSubscriptionStatusFromAuth(authController) == SubStatus.active;
 final subStatus = _resolveSubscriptionStatusFromAuth(authController);


   // même logique de loading que le body pour l'AppBar
      final bool appBarLoading =
          _pageBootLoading.value ||
          authController.tokenLoading.value ||
          authController.user == null ||
          subStatus == SubStatus.loading ||
          (authController.user?.hasStore == true && store == null);

      return Scaffold(
        backgroundColor: theme.colorScheme.surface,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: theme.colorScheme.surface,
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          title: Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 12,right: 12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () => Get.back(),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(Icons.arrow_back_ios_new, size: 18, color: theme.colorScheme.onSurface),
                  ),
                ),
              ),
              const SizedBox(width: 20),
               // ===== Title avec shimmer quand loading =====
        Flexible(
          child: appBarLoading
              ? _ShimmerBar(width: 140, height: 20, base: shimmerBase, high: shimmerHigh, radius: 6)
              : Text(
                  storeName,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.onSurface.withOpacity(0.9),
                  ),
                ),
        ),
      ],
    ),

          actions: [
      if (appBarLoading) ...[
        const SizedBox(width: 8),
      
        _ShimmerPill(width: 60, base: shimmerBase, high: shimmerHigh),
        const SizedBox(width: 10),
      ] else if (store != null && subActive) ...[
        TextButton(
          onPressed: () => Get.to(() => EditStorePage()),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
          child: Text("edit".tr, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600)),
        ),
        const SizedBox(width: 16),
      ] else ...[
        // pas de bouton lorsque pas abonné ou pas de store (et pas en chargement)
      ],
    ],
        ),

        body: SafeArea(
          child: Obx(() {
            final user     = authController.user;
            final myStore  = storeController.myStore.value;
            final hasStoreFlag = user?.hasStore == true;

            final subStatus = _resolveSubscriptionStatusFromAuth(authController);
            final bool subIsActive = subStatus == SubStatus.active;

            // ====== Shimmer au boot / refresh contrôlé ======
            final bool isBootLoading =
                _pageBootLoading.value || authController.tokenLoading.value || user == null ||
                // si l'API dit qu'il a un store mais qu'on ne l'a pas encore chargé, on reste en skeleton
                (hasStoreFlag && myStore == null);

            if (isBootLoading) {
              return _PageSkeleton(base: shimmerBase, high: shimmerHigh, tileStroke: tileStroke);
            }

            // Sécurité : si abonnement en "loading", garder le skeleton
            if (subStatus == SubStatus.loading) {
              return _PageSkeleton(base: shimmerBase, high: shimmerHigh, tileStroke: tileStroke);
            }

            // ====== Pas d'abonnement ======
            if (!subIsActive) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _NoSubscriptionState(
                  onSubscribe: () => Get.to(() => StoreSubscriptionPage()),
                  onRefresh: _refreshUserAndStore,
                ),
              );
            }

            // ====== Abonnement actif MAIS pas de store ======
            if (myStore == null) {
              return Padding(
               padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _SubscribedNoStoreState(
                  onCreate: () =>  Get.to(() => StoreInformations()),
                  onRefresh: _refreshUserAndStore,
                ),
              );
            }

            // ====== Abonnement actif + store présent ======
            return _BoutigakUserContent(store: myStore);
          }),
        ),
      );
    });
  }
}


// ============================================================
//           État “Abonné actif MAIS pas de store” (nouveau)
// ============================================================
class _NoSubscriptionState extends StatelessWidget {
  final VoidCallback onSubscribe;
  final Future<void> Function() onRefresh;
  const _NoSubscriptionState({required this.onSubscribe, required this.onRefresh});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
       
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 640),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeOut,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: border),
                  boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Hero icon avec halo
                    Container(
                      width: 72,
                      height: 72,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary.withOpacity(0.12),
                            theme.colorScheme.primary.withOpacity(0.06),
                          ],
                        ),
                      ),
                      child: Icon(FontAwesomeIcons.solidBellSlash, size: 30, color: theme.colorScheme.primary),
                    ),
                    const SizedBox(height: 16),

                    Text(
                      "subscription_required".trParams({"fallback": "Abonnement requis"}),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w800,
                        color: onSurface,
                        letterSpacing: 0.2,
                      ),
                    ),
                    const SizedBox(height: 8),

                    Text(
                      "subscription_desc".trParams({"fallback": "Active ton abonnement pour déverrouiller les commandes, les paiements et plus encore."}),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.4,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),

                    const SizedBox(height: 18),

                    // ---- Avantages Boutigak
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "benefits_title".trParams({"fallback": "Pourquoi choisir Boutigak ?"}),
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w800, color: onSurface),
                      ),
                    ),
                    const SizedBox(height: 10),

                    const _BenefitRow(
                      icon: Icons.storefront,
                      titleKey: "benefit_online_store",
                      titleFallback: "Boutique en ligne prête en quelques minutes",
                      descKey: "benefit_online_store_desc",
                      descFallback: "Crée et personnalise ta boutique sans compétences techniques.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.link,
                      titleKey: "benefit_share_link",
                      titleFallback: "Lien public qui liste tous tes articles",
                      descKey: "benefit_share_link_desc",
                      descFallback: "Partage un lien unique pour présenter tout ton catalogue.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.shopping_cart_checkout,
                      titleKey: "benefit_online_orders",
                      titleFallback: "Commandes en ligne",
                      descKey: "benefit_online_orders_desc",
                      descFallback: "Reçois et gère les commandes en temps réel depuis ton tableau de bord.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.payments,
                      titleKey: "benefit_online_payments",
                      titleFallback: "Paiements en ligne sécurisés",
                      descKey: "benefit_online_payments_desc",
                      descFallback: "Accepte les paiements (carte / mobile money) en toute sécurité.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.analytics_outlined,
                      titleKey: "benefit_analytics",
                      titleFallback: "Statistiques & suivi des ventes",
                      descKey: "benefit_analytics_desc",
                      descFallback: "Visualise les performances : vues, paniers, ventes et tendances.",
                    ),
                    const SizedBox(height: 10),
                    const _BenefitRow(
                      icon: Icons.share_outlined,
                      titleKey: "benefit_social",
                      titleFallback: "Partage social en 1 clic",
                      descKey: "benefit_social_desc",
                      descFallback: "Diffuse tes produits sur WhatsApp, Instagram et Facebook.",
                    ),

                    const SizedBox(height: 18),
                    Container(height: 1, width: 64, color: border),
                    const SizedBox(height: 18),

                    Wrap(
                      spacing: 12,
                      runSpacing: 10,
                      alignment: WrapAlignment.center,
                      children: [
                        OutlinedButton.icon(
                          onPressed: onRefresh,
                          icon: const Icon(Icons.refresh, size: 18),
                          label: Text("refresh".trParams({"fallback": "Rafraîchir"})),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            side: BorderSide(color: border),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: onSubscribe,
                      
                          label: Text("subscribe_now".trParams({"fallback": "S’abonner maintenant"})),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            elevation: 0,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}


/// Une ligne “avantage” avec icône, titre et description.
class _BenefitRow extends StatelessWidget {
  final IconData icon;
  final String titleKey;
  final String titleFallback;
  final String descKey;
  final String descFallback;

  const _BenefitRow({
    required this.icon,
    required this.titleKey,
    required this.titleFallback,
    required this.descKey,
    required this.descFallback,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final border = theme.dividerColor.withOpacity(0.2);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: border),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icône dans un petit carré doux
          Container(
            width: 36,
            height: 36,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: theme.colorScheme.primary.withOpacity(0.10),
            ),
            child: Icon(icon, size: 20, color: theme.colorScheme.primary),
          ),
          const SizedBox(width: 12),

          // Titre + description
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  titleKey.trParams({"fallback": titleFallback}),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.onSurface.withOpacity(0.95),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  descKey.trParams({"fallback": descFallback}),
                  style: TextStyle(
                    fontSize: 13,
                    height: 1.35,
                    color: theme.colorScheme.onSurface.withOpacity(0.72),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


class _SubscribedNoStoreState extends StatelessWidget {
  final VoidCallback onCreate;
  final Future<void> Function() onRefresh;

  const _SubscribedNoStoreState({
    required this.onCreate,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Hero icon avec halo
              Container(
                width: 72,
                height: 72,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.orange.withOpacity(0.14),
                      Colors.orange.withOpacity(0.07),
                    ],
                  ),
                ),
                child: Icon(FontAwesomeIcons.storeSlash, size: 30, color: Colors.orange),
              ),
              const SizedBox(height: 16),

              Text(
                "sub_active_no_store_title".trParams({"fallback": "Tu as un abonnement actif, mais pas encore de store"}),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                  color: onSurface,
                  letterSpacing: 0.2,
                ),
              ),
              const SizedBox(height: 8),

              Text(
                "sub_active_no_store_desc".trParams({
                  "fallback": "Crée ton store pour commencer à ajouter des produits et recevoir des commandes."
                }),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  height: 1.4,
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),

              const SizedBox(height: 18),
              Container(height: 1, width: 64, color: border),
              const SizedBox(height: 18),

              // Boutons d’action
              Wrap(
                spacing: 12,
                runSpacing: 10,
                alignment: WrapAlignment.center,
                children: [
                  OutlinedButton.icon(
                    onPressed: onRefresh,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: Text("refresh".trParams({"fallback": "Rafraîchir"})),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                      side: BorderSide(color: border),
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: onCreate,
                   
                    label: Text("create_store".trParams({"fallback": "Créer mon store"})),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                      elevation: 0,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}



/* ===========================
   Shimmer primitives utilisées dans l'app bar
   (tu as déjà les tiennes pour la page, on garde juste ces deux)
=========================== */

class _ShimmerBar extends StatelessWidget {
  final double width;
  final double height;
  final double radius;
  final Color base;
  final Color high;
  const _ShimmerBar({
    required this.width,
    required this.height,
    required this.base,
    required this.high,
    this.radius = 6,
  });

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (rect) => LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [base, high, base],
        stops: const [0.1, 0.3, 0.6],
      ).createShader(rect),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}

class _ShimmerPill extends StatelessWidget {
  final double width;
  final double height;
  final Color base;
  final Color high;
  const _ShimmerPill({
    required this.width,
    this.height = 28,
    required this.base,
    required this.high,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8, left: 8),
      child: ShaderMask(
        shaderCallback: (rect) => LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [base, high, base],
          stops: const [0.1, 0.3, 0.6],
        ).createShader(rect),
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: base,
            borderRadius: BorderRadius.circular(999),
          ),
        ),
      ),
    );
  }
}

/* ============================================================
 *                        CONTENU "OK"
 * ============================================================ */
class _BoutigakUserContent extends StatelessWidget {
  final dynamic store;
  const _BoutigakUserContent({required this.store});

  @override
  Widget build(BuildContext context) {
   
    final followersCount = store?.followersCount ?? 0;
    final opening = store?.openingTime;
    final closing = store?.closingTime;
    final images = store?.images ?? const [];
    final firstImage = images.isNotEmpty ? (images.first ?? "") : "";

    final hasBadgeCtrl = Get.isRegistered<BadgeController>();
    final badgeController = hasBadgeCtrl ? Get.find<BadgeController>() : null;

    return Column(
      children: [
        // ====== Header réel ======
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: SizedBox(
                  width: 120,
                  height: 120,
                  child: firstImage.isEmpty
                      ? _imagePlaceholder(context, width: 120, height: 120)
                      : CachedImageWidget(
                          imageUrl: firstImage,
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          borderRadius: BorderRadius.circular(12),
                        ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {},
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                      child: Text(
                        "$followersCount " + "followers".tr,
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                      ),
                    ),
                     SizedBox(height: 16.h),
                    _OutlineInfo(
                      icon: Icons.access_time,
                      label: (opening != null && closing != null && opening.isNotEmpty && closing.isNotEmpty)
                          ? "$opening - $closing"
                          : "—",
                    ),
                 
                    // Obx(() {
                    //   final isOpen = storeController.isStoreOpen.value;
                    //   final color = isOpen ? Colors.orange : Colors.green;
                    //   return _OutlineAction(
                    //     icon: isOpen ? Icons.store : Icons.store_mall_directory_outlined,
                    //     label: isOpen ? "close_store".tr : "open_store".tr,
                    //     color: color,
                    //     onTap: () => storeController.toggleStoreStatus(!isOpen),
                    //   );
                    // }),
                  ],
                ),
              ),
            ],
          ),
        ),

        // ====== Menu réel ======
        Expanded(
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            children: [
              _ModernTile(
                title: 'list_of_products'.tr,
                icon: FontAwesomeIcons.list,
                onTap: () => Get.to(() => StoreProductPage()),
              ),
              _OrdersTile(
                title: 'store_orders'.tr,
                icon: FontAwesomeIcons.cartShopping,
                badgeController: badgeController,
                onTap: () {
                  Get.to(() => MyStoreOrdersPage());
                  badgeController?.resetBadge('store-order');
                },
              ),
              _ModernTile(
                title: 'payment_method'.tr,
                icon: FontAwesomeIcons.creditCard,
                onTap: () => Get.to(() => PaymentProvidersPage()),
              ),
              _ModernTile(
                title: 'store_location'.tr,
                icon: FontAwesomeIcons.locationDot,
                onTap: () => Get.to(() => const StoreLocationManagementPage()),
              ),
              _ModernTile(
                title: 'subscription'.tr,
                icon: FontAwesomeIcons.solidBell,
                onTap: () => Get.to(() => StoreSubscriptionPage()),
              ),
              _ModernTile(
                title: 'social_media'.tr,
                icon: FontAwesomeIcons.at,
                onTap: () => Get.to(() => SocialMediaLinksPage(storeId: store?.id ?? 0)),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _imagePlaceholder(BuildContext context, {double width = 120, double height = 120}) {
    final theme = Theme.of(context);
    return Container(
      width: width,
      height: height,
      color: theme.colorScheme.surfaceVariant.withOpacity(0.25),
      alignment: Alignment.center,
      child: Icon(Icons.image_not_supported, color: theme.disabledColor),
    );
  }
}

  // ========= Helpers visuels =========



/* ============================================================
 *                   SKELETON / SHIMMER WIDGETS
 *   - Couleurs passées depuis la page (base/high) pour cohérence
 *   - Formes/tailles identiques aux widgets réels
 * ============================================================ */

class _PageSkeleton extends StatelessWidget {
  final Color base;
  final Color high;
  final Color tileStroke;
  const _PageSkeleton({required this.base, required this.high, required this.tileStroke});

  @override
  Widget build(BuildContext context) {
    // Reproduit la page: header + liste de 6 tuiles
    return Column(
      children: [
        // ----- Header skeleton -----
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _ShimmerBox(width: 120, height: 120, radius: 12, base: base, high: high),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _ShimmerPill(width: 110, height: 32, base: base, high: high),
                    const SizedBox(height: 10),
                    _ShimmerOutlined(width: 120, height: 36, base: base, high: high),
                  ],
                ),
              ),
            ],
          ),
        ),
        // ----- Menu skeleton -----
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            itemCount: 6,
            itemBuilder: (_, __) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 6),
                child: _CardSkeleton(
                  base: base,
                  high: high,
                  stroke: tileStroke,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _CardSkeleton extends StatelessWidget {
  final Color base;
  final Color high;
  final Color stroke;
  const _CardSkeleton({required this.base, required this.high, required this.stroke});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Theme.of(context).colorScheme.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
        side: BorderSide(color: stroke),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 14.w),
        child: Row(
          children: [
            _ShimmerBox(width: 40.w, height: 40.w, radius: 12.r, base: base, high: high),
            SizedBox(width: 12.w),
            Expanded(child: _ShimmerBar(width: double.infinity, height: 16, base: base, high: high, radius: 8)),
            SizedBox(width: 12.w),
            _ShimmerCircle(size: 22, base: base, high: high),
          ],
        ),
      ),
    );
  }
}

// --- Primitives (boîte, barre, pill, outlined) ---

class _ShimmerBox extends StatelessWidget {
  final double? width;
  final double? height;
  final double radius;
  final Color base;
  final Color high;
  const _ShimmerBox({this.width, this.height, this.radius = 12, required this.base, required this.high});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      direction: ShimmerDirection.ltr,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: base, // important: pas de blanc dur
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}


class _ShimmerCircle extends StatelessWidget {
  final double size;
  final Color base;
  final Color high;
  const _ShimmerCircle({required this.size, required this.base, required this.high});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(color: base, shape: BoxShape.circle),
      ),
    );
  }
}

class _ShimmerOutlined extends StatelessWidget {
  final double width;
  final double height;
  final Color base;
  final Color high;
  const _ShimmerOutlined({required this.width, required this.height, required this.base, required this.high});

  @override
  Widget build(BuildContext context) {
    final border = Theme.of(context).dividerColor.withOpacity(0.4);
    return Shimmer.fromColors(
      baseColor: base,
      highlightColor: high,
      period: const Duration(milliseconds: 1150),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: base,
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(color: border),
        ),
      ),
    );
  }
}

// ===================== TILES =====================

class _ModernTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final int badgeCount;
  final VoidCallback onTap;

  const _ModernTile({
    required this.title,
    required this.icon,
    required this.onTap,
    this.badgeCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: _CardContainer(
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 14.w),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: theme.colorScheme.primary.withOpacity(0.1),
                  ),
                  child: FaIcon(icon, size: 18.sp, color: theme.colorScheme.primary),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (badgeCount > 0)
                  badges.Badge(
                    badgeAnimation: const badges.BadgeAnimation.scale(),
                    position: badges.BadgePosition.topEnd(top: -4, end: -2),
                    badgeStyle: badges.BadgeStyle(
                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
                      badgeColor: Colors.red.shade100,
                    ),
                    badgeContent: Text(
                      badgeCount > 9 ? '9+' : '$badgeCount',
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    child: const SizedBox(width: 20, height: 20),
                  ),
                SizedBox(width: 8.w),
                Icon(Icons.chevron_right, size: 22.sp, color: theme.disabledColor),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// tuile commandes (sécurise l’accès au BadgeController)
class _OrdersTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final BadgeController? badgeController;
  final VoidCallback onTap;

  const _OrdersTile({
    required this.title,
    required this.icon,
    required this.onTap,
    this.badgeController,
  });

  @override
  Widget build(BuildContext context) {
    final count = badgeController?.getModuleCount('store-order') ?? 0;
    return _ModernTile(title: title, icon: icon, onTap: onTap, badgeCount: count);
  }
}

// ===================== Reusable UI bits =====================

class _CardContainer extends StatelessWidget {
  final Widget child;
  const _CardContainer({required this.child});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Material(
      color: theme.colorScheme.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
        side: BorderSide(color: theme.dividerColor.withOpacity(0.2)),
      ),
      child: child,
    );
  }
}

class _OutlineInfo extends StatelessWidget {
  final IconData icon;
  final String label;
  const _OutlineInfo({required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(color: theme.dividerColor.withOpacity(0.4)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12.sp, color: onSurface),
          SizedBox(width: 8.w),
          Text(
            label,
            style: TextStyle(color: onSurface, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
}

class _OutlineAction extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;
  const _OutlineAction({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      onPressed: onTap,
      
      label: Text(label, style: TextStyle(color: color, fontWeight: FontWeight.w600)),
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
          side: BorderSide(color: color.withOpacity(0.5)),
        ),
      ),
    );
  }
}


























































































class MyStoreitemsListViewWidget extends StatelessWidget {
  final Category? category;
  final List<Item> items;
  final String storeImage;

  MyStoreitemsListViewWidget({
    Key? key,
    this.category,
    required this.items,
    required this.storeImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final StoreController storeController = Get.find<StoreController>();

    // print('item id: ${items[0].id}');
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Obx(() {
      // Show loader when items are being fetched
      if (storeController.isLoadingItems.value) {
        return _buildShimmerEffect(context, screenWidth, sidePadding);
      }

      // Show "no items" message only after loading is complete
      if (items.isEmpty) {
        return Center(
          child: Text("No items available"),
        );
      }

      return _buildItemsList(context, screenWidth, sidePadding);
    });
  }

  Widget _buildShimmerEffect(
      BuildContext context, double screenWidth, double sidePadding) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                height: 20,
                width: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _shimmerAnimation(),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(
              left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 517,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return _buildShimmerItem(context, screenWidth);
              },
              childCount: 6, // Show 6 shimmer items
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerItem(BuildContext context, double screenWidth) {
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shimmer image placeholder
          Container(
            width: screenWidth * 0.4388,
            height: screenWidth * 0.5485,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: _shimmerAnimation(),
          ),
          SizedBox(height: 8),
          // Shimmer text placeholders
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 13,
                width: screenWidth * 0.3,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _shimmerAnimation(),
              ),
              SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    height: 13,
                    width: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _shimmerAnimation(),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _shimmerAnimation() {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.3, end: 1.0),
      duration: Duration(milliseconds: 1000),
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.grey[300]!.withOpacity(0.3),
                Colors.grey[100]!.withOpacity(value),
                Colors.grey[300]!.withOpacity(0.3),
              ],
              stops: [0.0, 0.5, 1.0],
            ),
          ),
        );
      },
      onEnd: () {
        // Animation will automatically repeat due to the way TweenAnimationBuilder works
      },
    );
  }

  Widget _buildItemsList(
      BuildContext context, double screenWidth, double sidePadding) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                "${items.length} Products",
                style: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.normal),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(
              left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 560,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return _buildItemWidget(context, items[index], screenWidth);
              },
              childCount: items.length,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemWidget(BuildContext context, Item item, double screenWidth) {
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              _showModalBottomSheet(context, item, storeImage);
            },
            child: Stack(
              children: [
                Container(
                  width: screenWidth * 0.4388,
                  height: screenWidth * 0.5485,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child:   CachedImageWidget(
                  imageUrl:
                      item.images.isNotEmpty ? '${item.images.first}' : '',
                  width: screenWidth * 0.4388,
                  height: screenWidth * 0.5485,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(8),
                ),
                ),
                if (item.hasPromotion ?? false)
                  Positioned(
                    top: 10,
                    left: 10,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                         borderRadius: BorderRadius.all( Radius.circular(5),),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.yellow,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.discount,
                                color: Theme.of(context).colorScheme.onSurface,
                                size: 15),
                          ),
                          SizedBox(width: 4),
                          Text(
                            "${item.promotionPercentage!.toStringAsFixed(0)}% Discount",
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 10,
                              fontWeight: AppFontWeights.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: screenWidth * 0.0407),
          Text(
            item.title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Row(
              mainAxisSize: MainAxisSize.min, // Évite de prendre tout l’espace
              children: [
                if (item.hasPromotion) ...[
                  Text(
                    '${(item.price / (1 - item.promotionPercentage! / 100)).toStringAsFixed(0)} mru',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: const Color.fromARGB(
                          255, 131, 131, 131), // Prix avant remise en gris
                      decoration: TextDecoration.lineThrough, // Barré
                    ),
                  ),
                  SizedBox(width: 5), // Espacement entre les prix
                ],
                Text(
                  '${item.price.toStringAsFixed(0)} mru',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary, // Prix après réduction
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addToOrder(Item item, int quantity, BuildContext context) {
    // Initialize OrderController with lazyPut to ensure it's only created when needed
    final OrderController orderController =
        Get.put(OrderController(initialOrderId: 1));
    final ItemController itemController = Get.put(ItemController());

    // Find if item exists in the order
    var existingOrderItem = orderController.items.firstWhereOrNull(
      (orderItem) => orderItem.itemId.value == item.id,
    );

    // If item exists, increment quantity
    if (existingOrderItem != null) {
      existingOrderItem.incrementQuantity();
    } else {
      // Add new item to order
      var newItem = OrderItemController(
        initialItemId: item.id!,
        initialQuantity: quantity,
      );
      orderController.addItem(newItem);
    }

    // Recalculate the total
    orderController.calculateTotal();

    Navigator.pop(context);
    orderController.update();
  }

  void _showModalBottomSheet(
      BuildContext context, Item item, String storeImage) {
    PageController pageController = PageController();
    int currentPage = 0;

    int quantity = 1;
    OrderController orderController =
        Get.put(OrderController(initialOrderId: 1));
    ItemController itemController = Get.put(ItemController());
    TextEditingController promotionController = TextEditingController();

    showModalBottomSheet(
      backgroundColor: Theme.of(context).colorScheme.surface,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    Container(
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height * .47,
                      child: ImageSlider(
                        pageController: pageController,
                        photos: item.images.map((image) => '$image').toList(),
                        currentPage: currentPage,
                        onPageChanged: (int page) =>
                            setState(() => currentPage = page),
                        borderRadius: 0,
                      ),
                    ),
                    Positioned(
                      top: 60,
                      right: 10,
                      child: ClipOval(
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                          child: Container(
                            color: AppColors.onBackground.withOpacity(0.2),
                            child: IconButton(
                              iconSize: 20,
                              icon: const Icon(
                                  FontAwesomeIcons.upRightFromSquare,
                                  color: AppColors.background),
                              onPressed: () => print('Share Icon Tapped!'),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 60,
                      left: 10,
                      child: ClipOval(
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                          child: Container(
                            color: AppColors.onBackground.withOpacity(0.3),
                            child: IconButton(
                              iconSize: 20,
                              icon: const Icon(FontAwesomeIcons.chevronDown,
                                  color: AppColors.background),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                // Scrollable info section
                Expanded(
                  child: Container(
                    width: double.infinity,
                    color: Colors.transparent,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
                        ),
                        child: MyStoreInfoSectionWidget(
                          boutiquePhotoUrl: storeImage,
                          item: item,
                          orderController: orderController,
                          dominantColor: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),

                // Bottom section with actions
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                        bottom: 30.0, left: 25, right: 25, top: 10),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Price (mru)",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context).disabledColor,
                                  ),
                                ),
                                Text(
                                  "${(item.price * quantity).toStringAsFixed(0)} ",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface,
                                  ),
                                ),
                              ],
                            ),
                            Spacer(),
                            Row(
                              children: [
                                Container(
                                  width: 80,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () async {
                                      // Close the bottom sheet first for smoother navigation
                                      Navigator.of(context).pop();

                                      // Navigate to edit page and handle return
                                      final result = await Get.to(() => SellStorePage(itemId: item.id));

                                      // Refresh store data when returning from edit
                                      if (result == true) {
                                        final storeController = Get.find<StoreController>();
                                        // Use the new refresh method for smoother updates
                                        await storeController.refreshStoreItems();
                                        storeController.selectAllCategories();
                                      }
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: const Text(
                                        "Edit",
                                        style: TextStyle(
                                          color: AppColors.surface,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10),
                                Container(
                                  width: 120,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () async {
                                      _showPromotionDialog(context, item);
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        item.hasPromotion
                                            ? "Promo: ${item.promotionPercentage}%" // Si promotion active, affiche le pourcentage
                                            : "Set Promotion", // Sinon affiche "Set Promotion"
                                        style: const TextStyle(
                                          color: AppColors.surface,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10),
                                Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: AppColors.error,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () async {
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return AlertDialog(
                                            title: const Text("Confirm Deletion"),
                                            content: const Text(
                                                "Are you sure you want to delete this item?"),
                                            actions: [
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                child: const Text("Cancel"),
                                              ),
                                              ElevatedButton(
                                                onPressed: () async {
                                                  // Close dialogs first
                                                  Navigator.of(context).pop(); // Ferme le dialog
                                                  Navigator.of(context).pop(); // Ferme le bottom sheet

                                                  // Delete item and refresh list
                                                  await itemController.deleteStoreItem(item.id!);
                                                  final storeController = Get.find<StoreController>();
                                                  await storeController.refreshStoreItems();
                                                  storeController.selectAllCategories();
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: AppColors.error,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(15),
                                                  ),
                                                ),
                                                child: const Text(
                                                  "Delete",
                                                  style: TextStyle(
                                                      color: AppColors.surface),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: const Icon(
                                        Icons.delete,
                                        color: AppColors.surface,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

// 🎯 **Fonction qui affiche la boîte de dialogue pour entrer le pourcentage de promotion**
  void _showPromotionDialog(BuildContext context, Item item) {
    TextEditingController promotionController = TextEditingController();
    final StoreController storeController = Get.put(StoreController());
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("Set Promotion"),
          content: TextField(
            controller: promotionController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(hintText: "Enter discount percentage"),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Cancel"),
            ),
            TextButton(
              onPressed: () async {
                double? discount = double.tryParse(promotionController.text);
                if (discount != null && discount > 0) {
                  await storeController.setPromotionForItem(item.id!, discount);
                  Navigator.pop(context); // Fermer le dialogue après validation
                } else {
                  // Get.snakbar("Error", "Please enter a valid discount percentage");
                }
              },
              child: Text("Apply"),
            ),
            if (item.hasPromotion)
              Container(
                width: 140,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.redAccent,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextButton(
                  onPressed: () async {
                    await storeController.removePromotionForItem(item.id!);
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    child: const Text(
                      "Remove Promotion",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class MyStoreInfoSectionWidget extends StatefulWidget {
  final String boutiquePhotoUrl;
  final Item item;
  final OrderController orderController;

  final Color? dominantColor;

  MyStoreInfoSectionWidget({
    Key? key,
    required this.boutiquePhotoUrl,
    required this.item,
    required this.orderController,
    this.dominantColor,
  }) : super(key: key);

  @override
  _MyStoreInfoSectionWidgetState createState() =>
      _MyStoreInfoSectionWidgetState();
}

class _MyStoreInfoSectionWidgetState extends State<MyStoreInfoSectionWidget> {
  bool isDescriptionSelected = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: ListView(
        shrinkWrap: true,
        primary: false,
        children: [
          // Title + Store info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Titre + Vendeur
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.item.title,
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          "by ",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          widget.item.userName ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).disabledColor,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Date
          Row(
            children: [
              const Spacer(),
              Text(
                widget.item.createdAt != null
                    ? timeago.format(widget.item.createdAt!)
                    : 'Unknown',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              )
            ],
          ),

          const SizedBox(height: 16),

          // Description Section
          Text(
            "Description",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.item.description,
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
            ),
          ),

          const SizedBox(height: 24),

          // Details Section
           Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Details",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow("Condition:", widget.item.condition),
                _buildDetailRow("Category:", widget.item.categoryName ?? "Unknown"),
                _buildDetailRow("Brand:", widget.item.brandName ?? "Unknown"),
                // Add category details if available
                if (widget.item.categoryItemDetails.isNotEmpty)
                  ...widget.item.categoryItemDetails.map<Widget>((detail) {
                    // Pick label based on locale, fallback to English
                    String label = detail.labelEn;
                    if (Get.locale?.languageCode == 'ar') {
                      label = detail.labelAr;
                    } else if (Get.locale?.languageCode == 'fr') {
                      label = detail.labelFr;
                    }
                    String value = detail.value.toString();
                    return _buildDetailRow("$label:", value);
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

 

   Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

}

class InfoSectionWidget extends StatefulWidget {
  final String boutiquePhotoUrl;
  final Item item;
  // final OrderController orderController;
  final bool isFavorited;
  final Color? dominantColor;
  final void Function()? toggleFavorite;

  InfoSectionWidget({
    Key? key,
    required this.boutiquePhotoUrl,
    required this.item,
    //  required this.orderController,
    required this.isFavorited,
    required this.toggleFavorite,
    this.dominantColor,
  }) : super(key: key);

  @override
  _InfoSectionWidgetState createState() => _InfoSectionWidgetState();
}

class _InfoSectionWidgetState extends State<InfoSectionWidget> {
  bool isDescriptionSelected = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: ListView(
        shrinkWrap: true,
        primary: false,
        children: [


    Row(
            children: [
              Container(
                width: 50.0,
                height: 50.0,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    image: DecorationImage(
                      image: NetworkImage(widget.boutiquePhotoUrl),
                      fit: BoxFit.cover,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: Offset(0.5, 0.5), // Position de l'ombre
                      ),
                    ]),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  widget.item.title,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              IconButton(
                icon: Icon(
                  widget.isFavorited ? Icons.favorite : Icons.favorite_border,
                  size: 30,
                  color: Colors.red,
                ),
                onPressed: widget.toggleFavorite,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Date
          Row(
            children: [
              const Spacer(),
              Text(
                widget.item.createdAt != null
                    ? timeago.format(widget.item.createdAt!)
                    : 'Unknown',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              )
            ],
          ),

          const SizedBox(height: 16),

          // Description Section
          Text(
            "Description",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.item.description,
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
            ),
          ),

          const SizedBox(height: 24),

          // Details Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Details",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow("Condition:", widget.item.condition),
                _buildDetailRow(
                    "Category:", widget.item.categoryName ?? "Unknown"),
                _buildDetailRow("Brand:", widget.item.brandName ?? "Unknown"),
                // Add category details if available
                if (widget.item.categoryItemDetails.isNotEmpty)
                  ...widget.item.categoryItemDetails.map<Widget>((detail) {
                    // Pick label based on locale, fallback to English
                    String label = detail.labelEn;
                    if (Get.locale?.languageCode == 'ar') {
                      label = detail.labelAr;
                    } else if (Get.locale?.languageCode == 'fr') {
                      label = detail.labelFr;
                    }
                    String value = detail.value.toString();
                    return _buildDetailRow("$label:", value);
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
} /* ───────────── Statut → Couleur ───────────── */







class StoreSubscriptionPage extends StatelessWidget {
  final AuthController authController = Get.find<AuthController>();

  StoreSubscriptionPage({super.key});

  // ====== helper commun pour WhatsApp ======
  Future<void> _openWhatsApp() async {
    const phone = "22238407840"; // ← remplace par ton numéro (sans +)
    final url = Uri.parse("https://wa.me/$phone");
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      Get.snackbar(
        'error'.tr,
        'WhatsApp not installed or URL invalid',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('subscription_title'.tr, ),
       
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Obx(() {
            final user = authController.user;
            final hasSubscription = authController.hasSubscription.value;

            // ——— Loading / état initial : skeleton
            if (user == null && hasSubscription == false) {
              return const _SubscriptionSkeleton();
            }

            // ——— Pas d’abonnement (UI carte améliorée + redirection WhatsApp)
            if (user == null || !hasSubscription) {
              return _EmptySubscription(
                onAction: _openWhatsApp,
              );
            }

            // ——— Abonné mais dates manquantes
            final startDate = user.subscriptionsStartDate;
            final endDate = user.subscriptionsEndDate;
            if (startDate == null || endDate == null) {
              return _DataMissing(onAction: () async {
                await authController.getAndSaveUser();
              });
            }

            // ——— Détails d’abonnement
            return _SubscriptionDetailsCard(
              startDate: startDate,
              endDate: endDate,
              onRenew: _openWhatsApp, // même action WhatsApp
            );
          }),
        ),
      ),
    );
  }
}

// ================== UI components ==================

class _SubscriptionDetailsCard extends StatelessWidget {
  final DateTime startDate;
  final DateTime endDate;
  final VoidCallback onRenew;

  const _SubscriptionDetailsCard({
    required this.startDate,
    required this.endDate,
    required this.onRenew,
  });

  String _fmtDate(BuildContext context, DateTime d) {
    final ml = MaterialLocalizations.of(context);
    return ml.formatFullDate(d);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final now = DateTime.now();

    final totalDaysRaw = endDate.difference(startDate).inDays;
    final totalDays = totalDaysRaw <= 0 ? 1 : totalDaysRaw;
    final leftDaysRaw = endDate.difference(now).inDays;
    final remainingDays = leftDaysRaw > 0 ? leftDaysRaw : 0;
    final usedDays = (now.isBefore(startDate)) ? 0 : now.difference(startDate).inDays.clamp(0, totalDays);
    final consumed = (usedDays / totalDays).clamp(0.0, 1.0);

    final isActive = now.isBefore(endDate);
    final isExpiringSoon = remainingDays < 60;

    final startText = _fmtDate(context, startDate);
    final endText   = _fmtDate(context, endDate);

    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 640),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hero + titre + badge
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.16),
                          AppColors.primary.withOpacity(0.08),
                        ],
                      ),
                    ),
                    child: Icon(Icons.subscriptions, color: AppColors.primary),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'subscription_overview'.tr,
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800, color: onSurface),
                    ),
                  ),
                  _StatusChip(
                    label: isActive ? 'status_active'.tr : 'status_expired'.tr,
                    color: isActive ? Colors.green : Colors.red,
                    icon: isActive ? Icons.verified_user : Icons.error_outline,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Dates
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _DateColumn(title: 'start_date'.tr, date: startText),
                  _DateColumn(title: 'end_date'.tr,   date: endText),
                ],
              ),
              const SizedBox(height: 16),

              // Barre de progression (part consommée)
              _ProgressBar(
                progress: consumed,
                leadingLabel: 'consumed'.trParams({'days': usedDays.toString()}),
                trailingLabel: 'total_days'.trParams({'days': totalDays.toString()}),
              ),
              const SizedBox(height: 12),

              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'time_remaining'.trParams({'days': remainingDays.toString()}),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 16),

              if (isActive && isExpiringSoon)
                _AlertCard(
                  text: 'renew_soon_notice'.tr,
                  color: Colors.orange,
                  icon: Icons.notifications_active_outlined,
                ),
              if (!isActive)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: _AlertCard(
                    text: 'expired_notice'.tr,
                    color: Colors.red,
                    icon: Icons.report_gmailerrorred_outlined,
                  ),
                ),

              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: onRenew,
                      icon: const Icon(Icons.autorenew),
                      label: Text(isActive ? 'renew_now'.tr : 'renew_subscription'.tr),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.surface,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        elevation: 0,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('subscription_tips_title'.tr, style: const TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 6),
              _TipLine(text: 'subscription_tip_visibility'.tr),
              _TipLine(text: 'subscription_tip_reminder'.tr),
            ],
          ),
        ),
      ),
    );
  }
}

class _StatusChip extends StatelessWidget {
  final String label;
  final Color color;
  final IconData icon;
  const _StatusChip({required this.label, required this.color, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.12),
        borderRadius: BorderRadius.circular(999),
        border: Border.all(color: color.withOpacity(0.4)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Text(label, style: TextStyle(color: color, fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}

class _DateColumn extends StatelessWidget {
  final String title;
  final String date;
  const _DateColumn({required this.title, required this.date});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
        const SizedBox(height: 4),
        Text(date, style: TextStyle(fontSize: 12, color: theme.hintColor)),
      ],
    );
  }
}

class _ProgressBar extends StatelessWidget {
  final double progress; // 0..1 consommé
  final String leadingLabel;
  final String trailingLabel;
  const _ProgressBar({required this.progress, required this.leadingLabel, required this.trailingLabel});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final consumed = progress.clamp(0.0, 1.0);
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Stack(
            children: [
              Container(height: 12, color: theme.dividerColor.withOpacity(0.25)),
              FractionallySizedBox(
                widthFactor: consumed,
                child: Container(
                  height: 12,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primary, AppColors.primary.withOpacity(0.7)],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 6),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(leadingLabel, style: TextStyle(color: theme.hintColor, fontSize: 12)),
            Text(trailingLabel, style: TextStyle(color: theme.hintColor, fontSize: 12)),
          ],
        ),
      ],
    );
  }
}

class _AlertCard extends StatelessWidget {
  final String text;
  final MaterialColor color;
  final IconData icon;

  const _AlertCard({
    required this.text,
    required this.color,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.4)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: TextStyle(color: color.shade700, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}

class _TipLine extends StatelessWidget {
  final String text;
  const _TipLine({required this.text});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Icon(Icons.check_circle_outline, size: 16, color: theme.hintColor),
        const SizedBox(width: 6),
        Expanded(child: Text(text)),
      ],
    );
  }
}

// ====== Empty Subscription (UI carte + WhatsApp) ======
class _EmptySubscription extends StatelessWidget {
  final VoidCallback onAction; // WhatsApp
  const _EmptySubscription({required this.onAction});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Hero icon avec halo
              Container(
                width: 72,
                height: 72,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withOpacity(0.12),
                      AppColors.primary.withOpacity(0.06),
                    ],
                  ),
                ),
                child: Icon(Icons.subscriptions_outlined, size: 30, color: AppColors.primary),
              ),
              const SizedBox(height: 16),

              Text(
                'no_subscription_title'.tr,
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800, color: onSurface),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'no_subscription_subtitle'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, height: 1.4, color: theme.colorScheme.onSurface.withOpacity(0.7)),
              ),
              const SizedBox(height: 18),
              Container(height: 1, width: 64, color: border),
              const SizedBox(height: 18),

              ElevatedButton.icon(
                onPressed: onAction,
                icon:  Icon(FontAwesomeIcons.whatsapp,),
                label: Text('get_subscription'.tr),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.surface,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ====== Data Missing (UI carte) ======
class _DataMissing extends StatelessWidget {
  final VoidCallback onAction;
  const _DataMissing({required this.onAction});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface.withOpacity(0.9);
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 72,
                height: 72,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.withOpacity(0.14),
                      Colors.orange.withOpacity(0.07),
                    ],
                  ),
                ),
                child: const Icon(Icons.info_outline, size: 30, color: Colors.orange),
              ),
              const SizedBox(height: 16),
              Text('missing_data_title'.tr,
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800, color: onSurface),
                  textAlign: TextAlign.center),
              const SizedBox(height: 8),
              Text('missing_data_subtitle'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, height: 1.4, color: theme.colorScheme.onSurface.withOpacity(0.7))),
              const SizedBox(height: 18),
              Container(height: 1, width: 64, color: border),
              const SizedBox(height: 18),
              OutlinedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.refresh),
                label: Text('retry'.tr),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  side: BorderSide(color: border),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ====== Skeleton (style carte + shimmer) ======
class _SubscriptionSkeleton extends StatelessWidget {
  const _SubscriptionSkeleton();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final base = isDark ? Colors.white.withOpacity(0.10) : Colors.grey.shade300;
    final high = isDark ? Colors.white.withOpacity(0.25) : Colors.grey.shade100;
    final border = theme.dividerColor.withOpacity(0.2);
    final shadow = Colors.black.withOpacity(0.06);

    Widget bar(double w, double h, {double r = 10}) => Shimmer.fromColors(
          baseColor: base,
          highlightColor: high,
          period: const Duration(milliseconds: 1100),
          child: Container(width: w, height: h, decoration: BoxDecoration(color: base, borderRadius: BorderRadius.circular(r))),
        );

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 640),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeOut,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: border),
            boxShadow: [BoxShadow(color: shadow, blurRadius: 16, offset: const Offset(0, 6))],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // header (icon + title + chip)
              Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(shape: BoxShape.circle, color: base.withOpacity(0.6)),
                  ),
                  const SizedBox(width: 12),
                  Expanded(child: bar(180, 18, r: 6)),
                  const SizedBox(width: 10),
                  bar(110, 28, r: 999),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [bar(120, 12, r: 6), bar(120, 12, r: 6)],
              ),
              const SizedBox(height: 16),
              bar(double.infinity, 12),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [bar(120, 10, r: 6), bar(100, 10, r: 6)],
              ),
              const SizedBox(height: 16),
              bar(double.infinity, 44, r: 12),
            ],
          ),
        ),
      ),
    );
  }
}


// ==========================
class StoreLocationManagementPage extends StatefulWidget {
  const StoreLocationManagementPage({super.key});

  @override
  State<StoreLocationManagementPage> createState() =>
      _StoreLocationManagementPageState();
}

class _StoreLocationManagementPageState
    extends State<StoreLocationManagementPage> {
  final StoreController storeController = Get.find<StoreController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      storeController.fetchMyStoreLocations();
    });
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) return;

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      storeController.updateStorePosition(
        gmaps.LatLng(position.latitude, position.longitude),
      );
    } catch (e) {
      debugPrint('Error getting location: $e');
      Get.snackbar('error'.tr, 'location_error'.tr,
          snackPosition: SnackPosition.BOTTOM);
    }
  }

  Future<void> _openAddOrEdit({required bool isEditing}) async {
    await _getCurrentLocation();
    final saved = await Get.to(
      () => AddStoreLocationPage(isEditing: isEditing),
      fullscreenDialog: true,
    );
    if (saved == true) {
      await storeController.fetchMyStoreLocations();
    }
  }

  Future<void> _onRefresh() async {
    await storeController.fetchMyStoreLocations();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // force Rx reads (évite warning GetX)
      final loading = storeController.isLoadingStoreAdresses.value;
      final hasLocation = storeController.myStoreLocations.isNotEmpty;

      return Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.background,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          centerTitle: false,
          titleSpacing: 16,
          title: Text(
            'store_location'.tr, // traduction à ajouter
            style: const TextStyle(fontWeight: FontWeight.w700),
          ),
        ),
        floatingActionButton: !hasLocation
            ? FloatingActionButton(
                onPressed: () => _openAddOrEdit(isEditing: false),
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
                child: const Icon(Icons.add_location_alt),
              )
            : null,
        body: SafeArea(
          child: CustomScrollView(
            slivers: [
              CupertinoSliverRefreshControl(onRefresh: _onRefresh),

              // SHIMMER
              if (loading)
                const _StoreLocationShimmer()

              // EMPTY STATE
              else if (!hasLocation)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: _EmptyStoreLocation(
                    onAdd: () => _openAddOrEdit(isEditing: false),
                  ),
                )

              // LISTE (1 carte) - même UI que My addresses
              else
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 100),
                  sliver: SliverList.list(
                    children: [
                      Obx(() {
                        final loc = storeController.myStoreLocations.first;
                        return _StoreAddressCard(
                          name: (loc.name?.isNotEmpty ?? false)
                              ? loc.name!
                              : 'store_name'.tr,
                          address: (loc.address?.isNotEmpty ?? false)
                              ? loc.address!
                              : 'address'.tr,
                          onEdit: () => _openAddOrEdit(isEditing: true),
                        );
                      }),
                    ],
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }
}

// ====== Carte identique à “My addresses” ======
class _StoreAddressCard extends StatelessWidget {
  final String name;
  final String address;
  final VoidCallback onEdit;

  const _StoreAddressCard({
    required this.name,
    required this.address,
    required this.onEdit,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Material(
        color: AppColors.surface,
        elevation: 0,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onEdit,
          child: Container(
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.black12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.blueGrey.withOpacity(.08),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.location_on_outlined),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(name,
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w700)),
                      const SizedBox(height: 4),
                      Text(address,
                          style:
                              TextStyle(color: Colors.black.withOpacity(.7))),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  visualDensity: VisualDensity.compact,
                  onPressed: onEdit,
                  icon: const Icon(Icons.more_horiz),
                  tooltip: 'edit'.tr,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ====== Shimmer identique à la carte ======
class _ShimmerAddressCard extends StatelessWidget {
  const _ShimmerAddressCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.black12),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 14,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      height: 12,
                      width: 180,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 28,
                height: 28,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StoreLocationShimmer extends StatelessWidget {
  const _StoreLocationShimmer({super.key});
  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 100),
      sliver: SliverList.builder(
        itemCount: 1,
        itemBuilder: (_, __) => const _ShimmerAddressCard(),
      ),
    );
  }
}

// =======================
// Add / Edit Store Page
// =======================
class AddStoreLocationPage extends StatelessWidget {
  final StoreController storeController = Get.find<StoreController>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final bool isEditing;

  AddStoreLocationPage({super.key, this.isEditing = false}) {
    if (isEditing && storeController.myStoreLocations.isNotEmpty) {
      final location = storeController.myStoreLocations.first;
      nameController.text = location.name ?? '';
      addressController.text = location.address ?? '';
    } else {
      // Pré-remplissage intelligent du nom (si un nom connu existe)
      final fallbackName = storeController.myStoreLocations.isNotEmpty
          ? storeController.myStoreLocations.first.name
          : null;
      nameController.text =
          (fallbackName?.isNotEmpty == true) ? fallbackName! : 'Store';
    }
  }

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        // Map bord-à-bord
        top: false,
        bottom: false,
        child: Stack(
          children: [
            // MAP
            Obx(
              () => gmaps.GoogleMap(
                initialCameraPosition: gmaps.CameraPosition(
                  target: storeController.currentStorePosition.value,
                  zoom: 15,
                ),
                onTap: (gmaps.LatLng p) => storeController.updateStorePosition(p),
                markers: {
                  gmaps.Marker(
                    markerId: const gmaps.MarkerId('store_location'),
                    position: storeController.currentStorePosition.value,
                    draggable: true,
                    onDragEnd: (gmaps.LatLng np) =>
                        storeController.updateStorePosition(np),
                  ),
                },
                myLocationEnabled: true,
                myLocationButtonEnabled: true,
              ),
            ),

            // BOUTON RETOUR frosted
            Positioned(
              top: 12 + MediaQuery.of(context).padding.top,
              left: 12,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(28),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(.2),
                      borderRadius: BorderRadius.circular(28),
                      border: Border.all(color: Colors.white.withOpacity(.3)),
                    ),
                    child: IconButton(
                      icon:
                          const Icon(CupertinoIcons.xmark, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ),
              ),
            ),

            // PANEL BAS (form)
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(16, 10, 16, 16),
                decoration: const BoxDecoration(
                  color: Color(0xFFF7F7F7),
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 12,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // handle
                      Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.black26,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),

                      _ModernTextField(
                        controller: nameController,
                        label: 'store_name'.tr,
                        hint: 'Ex: Boutique Ahmed',
                        icon: Icons.store_outlined,
                        maxLength: 40,
                        validator: (v) {
                          if (v.isEmpty) return 'fill_all_fields'.tr;
                          if (v.length < 2) return 'Nom trop court';
                          return null;
                        },
                      ),
                      const SizedBox(height: 12),

                      _ModernTextField(
                        controller: addressController,
                        label: 'address'.tr,
                        hint: 'Rue, bâtiment, repère…',
                        icon: Icons.location_on_outlined,
                        maxLength: 140,
                        maxLines: 2,
                        validator: (v) {
                          if (v.isEmpty) return 'fill_all_fields'.tr;
                          if (v.length < 6) return 'Adresse trop courte';
                          return null;
                        },
                      ),

                      const SizedBox(height: 14),

                      // SAVE / UPDATE
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size(double.infinity, 52),
                            backgroundColor: Colors.black,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(14),
                            ),
                          ),
                          onPressed: () async {
                            if (!(formKey.currentState?.validate() ?? false)) {
                              return;
                            }

                            storeController.newStoreName.value =
                                nameController.text.trim();
                            storeController.newStoreAddress.value =
                                addressController.text.trim();

                            final ok = await Get.showOverlay(
                              asyncFunction: () async {
                                await storeController.saveMyStoreLocation(
                                  name: nameController.text.trim(),
                                  address: addressController.text.trim(),
                                  latitude: storeController
                                      .currentStorePosition.value.latitude,
                                  longitude: storeController
                                      .currentStorePosition.value.longitude,
                                );
                                return true;
                              },
                              loadingWidget: const Center(
                                  child: CircularProgressIndicator()),
                              opacity: 0.0,
                            );

                            if (ok) {
                              Get.back(result: true); // ferme la page
                              Get.snackbar('saved'.tr, 'address_saved'.tr,
                                  snackPosition: SnackPosition.BOTTOM);
                            }
                          },
                          child: Text(isEditing ? 'update'.tr : 'save'.tr),
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ==== Champ réutilisable style moderne ====
class _ModernTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final IconData icon;
  final int maxLength;
  final int maxLines;
  final String? Function(String value)? validator;

  const _ModernTextField({
    super.key,
    required this.controller,
    required this.label,
    required this.hint,
    required this.icon,
    this.maxLength = 50,
    this.maxLines = 1,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setSt) {
        controller.removeListener(() {});
        controller.addListener(() => setSt(() {}));

        return TextFormField(
          controller: controller,
          maxLines: maxLines,
          maxLength: maxLength,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: Icon(icon),
            suffixIcon: controller.text.isEmpty
                ? null
                : IconButton(
                    icon: const Icon(Icons.close_rounded),
                    tooltip: 'clear',
                    onPressed: () {
                      controller.clear();
                      setSt(() {});
                    },
                  ),
            counterText: '${controller.text.length}/$maxLength',
            filled: true,
            fillColor: Colors.white,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: BorderSide(color: Colors.black12.withOpacity(.15)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: BorderSide(color: AppColors.primary, width: 1.6),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
          validator: (v) {
            final text = v?.trim() ?? '';
            if (validator != null) return validator!(text);
            if (text.isEmpty) return 'missing_info'.tr;
            return null;
          },
        );
      },
    );
  }
}
class _EmptyStoreLocation extends StatelessWidget {
  final VoidCallback onAdd;
  const _EmptyStoreLocation({required this.onAdd, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: 100,
            width: 100,
            decoration: BoxDecoration(
              color: Colors.blueGrey.withOpacity(.08),
              borderRadius: BorderRadius.circular(28),
            ),
            child: const Icon(Icons.store_mall_directory_outlined,
                size: 44, color: Colors.grey),
          ),
          const SizedBox(height: 20),
          Text(
            'no_store_location'.tr, // ex: "Aucune localisation de magasin définie"
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
          ),
          const SizedBox(height: 8),
          Text(
            'add_store_location_hint'.tr, // ex: "Ajoutez la localisation du magasin pour que les clients vous trouvent."
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.black.withOpacity(.6),
            ),
          ),
          const SizedBox(height: 28),
          ElevatedButton.icon(
            onPressed: onAdd,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.onPrimary,
              minimumSize: const Size(double.infinity, 50),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(14),
              ),
            ),
            icon: const Icon(Icons.add_location_alt_outlined),
            label: Text('add_address'.tr),
          ),
        ],
      ),
    );
  }
}

/* ===========================
   Traductions à ajouter (FR/EN/AR)
   - 'store_location' : 'Localisation du magasin' / 'Store location' / 'موقع المتجر'
   - 'no_store_location' : 'Aucune localisation de magasin définie' / 'No store location set' / 'لا يوجد موقع متجر محدد'
   - 'add_store_location_hint' : 'Ajoutez la localisation du magasin pour que les clients vous trouvent.' / 'Add your store location so customers can find you.' / 'أضف موقع متجرك ليتسنى للعملاء العثور عليك.'
   - 'store_name' : 'Nom du magasin' / 'Store name' / 'اسم المتجر'
   - 'address' : 'Adresse' / 'Address' / 'العنوان'
   - 'save' : 'Enregistrer' / 'Save' / 'حفظ'
   - 'update' : 'Mettre à jour' / 'Update' / 'تحديث'
   - 'saved' : 'Enregistrée' / 'Saved' / 'تم الحفظ'
   - 'address_saved' : 'Adresse enregistrée avec succès' / 'Address saved successfully' / 'تم حفظ العنوان بنجاح'
   - 'error' : 'Erreur' / 'Error' / 'خطأ'
   - 'location_error' : 'Échec de l’obtention de la position' / 'Failed to get current location' / 'فشل الحصول على الموقع الحالي'
   - 'missing_info' : 'Informations manquantes' / 'Missing information' / 'معلومات ناقصة'
   - 'fill_all_fields' : 'Veuillez remplir tous les champs' / 'Please fill in all fields' / 'يرجى ملء جميع الحقول'
=========================== */
