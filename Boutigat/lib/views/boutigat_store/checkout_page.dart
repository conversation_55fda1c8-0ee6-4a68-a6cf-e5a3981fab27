import 'dart:ui';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/dashline.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';

import 'package:shimmer/shimmer.dart';
// ======= Helpers =======
double _asDouble(dynamic v) {
  if (v == null) return 0;
  if (v is num) return v.toDouble();
  if (v is String) {
    final cleaned = v.replaceAll(RegExp(r'[^0-9\.\-]'), '');
    return double.tryParse(cleaned) ?? 0;
  }
  return 0;
}

// Élément sentinelle “Gérer adresses”
Location _manageSentinel() => Location(
      id: -1,
      name: 'manage_addresses'.tr,
      address: '',
      latitude: null,
      longitude: null,
    );

// Supprime les doublons d’adresses
List<Location> _uniqueLocations(List<Location> source) {
  final unique = <Location>[];
  final seenIds = <int>{};
  for (final l in source) {
    if (l.id != null) {
      if (!seenIds.contains(l.id)) {
        seenIds.add(l.id!);
        unique.add(l);
      }
    } else {
      final exists = unique.any((e) =>
          e.name == l.name &&
          e.address == l.address &&
          e.latitude == l.latitude &&
          e.longitude == l.longitude);
      if (!exists) unique.add(l);
    }
  }
  return unique;
}

// ====================== CHECKOUT ======================

class CheckoutPage extends StatefulWidget {
  final OrderController orderController;
  final Store store;
  final Color dominentColor; // pour cohérence si tu veux la réutiliser ailleurs
  final Color surfaceColor;  // idem

  const CheckoutPage({
    Key? key,
    required this.orderController,
    required this.store,
    required this.dominentColor,
    required this.surfaceColor,
  }) : super(key: key);

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  OrderController get orderController => widget.orderController;

  @override
  void initState() {
    super.initState();
    // Précharge les données nécessaires
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (orderController.userLocations.isEmpty) {
        await orderController.fetchUserLocations();
      }
      // Si tu as une méthode pour charger les frais de livraison, décommente :
      // if (orderController.deliveryCharges.isEmpty) {
      //   await orderController.fetchDeliveryCharges();
      // }
      // Assure-toi que le total est prêt
      orderController.calculateTotal();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgTop = theme.colorScheme.primary;
    final bgMid = theme.colorScheme.primary.withOpacity(0.9);

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: _GlassScaffold(
        title: 'checkout_title'.tr.isEmpty ? 'Checkout' : 'checkout_title'.tr,
        bgTop: bgTop,
        bgMid: bgMid,
        child: Obx(() {
          final items = orderController.items; // List<OrderItemController>
          final locations = _uniqueLocations(orderController.userLocations.toList());
          final selectedLocation = orderController.selectedLocation.value;
          final isCOD = orderController.isCashOnDelivery.value;
          final isLoading = orderController.isLoading.value;

          // Montant livraison sélectionné
          final selectedDeliveryId = orderController.deliveryChargeID.value != 0
              ? orderController.deliveryChargeID.value
              : null;

          final selectedDelivery = orderController.deliveryCharges
              .firstWhereOrNull((c) => c.id == selectedDeliveryId);

          final deliveryAmount = _asDouble(selectedDelivery?.amount);
          final total = orderController.total.value;
          final subtotal = (total - deliveryAmount).clamp(0, double.infinity);

          final readyToOrder = orderController.isLocationSelected.value &&
              (orderController.deliveryChargeID.value != 0) &&
              !isLoading;

          return SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // --------- Store header (version glass) ---------
               

               
                // --------- Méthode de paiement (COD) ---------
                _GlassSection(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      
                      const _SectionTitle(title: 'payment'),
                    
                      const SizedBox(height: 12),

                      // Toggle COD
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.06),
                          borderRadius: BorderRadius.circular(14),
                          border: Border.all(color: Colors.white24),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        child: Row(
                          children: [
                            const Icon(Icons.payments_outlined, color: Colors.white),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'cash_on_delivery'.tr,
                                style: TextStyle(
                                  color: isCOD ? Colors.white : Colors.white70,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            Switch(
                              value: isCOD,
                              onChanged: (v) => orderController.isCashOnDelivery.value = v,
                              activeColor: Colors.white,
                              activeTrackColor: Colors.white30,
                              inactiveThumbColor: Colors.white70,
                              inactiveTrackColor: Colors.white24,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

               // --------- Frais de livraison ---------
// --------- Frais de livraison ---------
_GlassSection(
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const _SectionTitle(title: 'delivery_charge'),
      const SizedBox(height: 10),
      Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.04),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white24),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<int>(
            dropdownColor: Colors.white,
            iconEnabledColor: Colors.white,
            isExpanded: true,
            value: selectedDeliveryId,
            hint: Row(
              children: [
                Icon(Icons.local_shipping_outlined, size: 18, color: Colors.white70),
                SizedBox(width: 8),
                Text("delivery_charge".tr, style: TextStyle(color: Colors.white70)),
              ],
            ),
            // --- Options quand le menu est OUVERT (fond blanc → texte noir)
            items: orderController.deliveryCharges.map((DeliveryCharge dc) {
              return DropdownMenuItem<int>(
                value: dc.id,
                child: Row(
                  children: [
                    const Icon(Icons.local_shipping_outlined, size: 18, color: Colors.black87),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        dc.type.tr,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontWeight: FontWeight.w600, color: Colors.black87),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.06),
                        borderRadius: BorderRadius.circular(999),
                        border: Border.all(color: Colors.black12),
                      ),
                      child: Text(
                        '${dc.amount} ${'mru'.tr}',
                        style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 12, color: Colors.black87),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            // --- Rendu quand le menu est FERMÉ (fond glass → texte blanc) + CHIP BLANC
            selectedItemBuilder: (ctx) {
              return orderController.deliveryCharges.map((dc) {
                return Row(
                  children: [
                    const Icon(Icons.local_shipping_outlined, size: 18, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        dc.type,
                        style: const TextStyle(fontWeight: FontWeight.w600, color: Colors.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.12),
                        borderRadius: BorderRadius.circular(999),
                        border: Border.all(color: Colors.white30),
                      ),
                      child: Text(
                        '${dc.amount} ${'mru'.tr}',
                        style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 12, color: Colors.white),
                      ),
                    ),
                  ],
                );
              }).toList();
            },
            onChanged: (newValue) {
              orderController.deliveryChargeID.value = newValue ?? 0;
              orderController.calculateTotal();
            },
          ),
        ),
      ),
    ],
  ),
),

const SizedBox(height: 16),

// --------- Adresse de livraison ---------
_GlassSection(
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Laisse const si _SectionTitle traduit en interne ; sinon retire const et passe .tr ici.
      const _SectionTitle(title: 'delivery_address'),
      const SizedBox(height: 10),

      Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.04),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white24),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<Location>(
            dropdownColor: Colors.white,
            iconEnabledColor: Colors.white,
            isExpanded: true,
            value: selectedLocation,
            hint: Row(
              children: [
                const Icon(Icons.location_on_outlined, color: Colors.white70, size: 18),
                const SizedBox(width: 8),
                Text(
                  "select_delivery_address".tr,
                  style: const TextStyle(color: Colors.white70),
                ),
              ],
            ),

            // --- ITEMS (locations + divider + manage) ---
            items: [
              // 1) toutes les adresses utilisateur
              ...locations.map((l) => DropdownMenuItem<Location>(
                    value: l,
                    child: Row(
                      children: [
                        const Icon(Icons.location_on_outlined, color: Colors.black87, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          l.name ?? '',
                          style: const TextStyle(
                            fontWeight: FontWeight.w700,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  )),
              // 2) séparateur (désactivé)
              DropdownMenuItem<Location>(
                enabled: false,
                value: Location(
                  id: -2,
                  name: 'divider',
                  address: null,
                  latitude: null,
                  longitude: null,
                ),
                child: const Divider(height: 8, color: Colors.black26),
              ),
              // 3) gérer les adresses
              DropdownMenuItem<Location>(
                value: _manageSentinel(),
                child: Row(
                  children: [
                    const Icon(Icons.manage_search_outlined, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'manage_addresses'.tr,
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // --- SELECTED ITEM BUILDER : même nombre que items ---
            selectedItemBuilder: (ctx) {
              final allForBuilder = [
                ...locations,
                Location(
                  id: -2,
                  name: 'divider',
                  address: null,
                  latitude: null,
                  longitude: null,
                ),
                _manageSentinel(),
              ];

              return allForBuilder.map((l) {
                if (l.id == -2) {
                  // rendu “séparateur” si jamais sélectionné (théoriquement non)
                  return const SizedBox.shrink();
                }
                if (l.id == -1) {
                  // rendu quand “Gérer les adresses” serait sélectionné (rare)
                  return Row(
                    children: [
                      const Icon(Icons.manage_search_outlined, color: Colors.white, size: 18),
                      const SizedBox(width: 8),
                      Text(
                        'manage_addresses'.tr,
                        style: const TextStyle(
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  );
                }
                // rendu normal pour une adresse sélectionnée
                return Row(
                  children: [
                    const Icon(Icons.location_on_outlined, color: Colors.white, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      l.name ?? '',
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                  ],
                );
              }).toList();
            },

            onChanged: (Location? val) async {
              if (val == null) return;

              // clic sur "Gérer les adresses"
              if (val.id == -1) {
                final res = await Get.to(() => AddressManagementPage());
                if (res == true || res == null) {
                  await orderController.fetchUserLocations();
                }
                return;
              }

              // ignorer le séparateur (désactivé de toute façon)
              if (val.id == -2) return;

              orderController.selectedLocation.value = val;
              orderController.isLocationSelected.value = true;
            },
          ),
        ),
      ),
    ],
  ),
),


                const SizedBox(height: 16),

                // --------- Résumé de commande ---------
                _GlassSection(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const _SectionTitle(title: 'order_summary'),
                      const SizedBox(height: 10),

                      // Lignes d’articles
                      ...items.map((c) {
                        final tag = c.itemId.value.toString();
                        final item =
                            Get.find<ItemController>(tag: tag).selectedItem.value;
                        if (item == null) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              children: [
                                Container(
                                  width: 54,
                                  height: 54,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.08),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        height: 12,
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.08),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                      ),
                                      const SizedBox(height: 6),
                                      Container(
                                        height: 12,
                                        width: 120,
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.08),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Container(
                                  height: 12,
                                  width: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.08),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        final qty = c.quantity.value;
                        final line = (item.price * qty);
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            children: [
                              // (Optionnel) miniature produit si dispo
                              // ...
                              Expanded(
                                child: Text(
                                  '${item.title}  x$qty',
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w600, color: Colors.white),
                                ),
                              ),
                              const SizedBox(width: 10),
                              Text(
                                '${line.toStringAsFixed(2)} ${'mru'.tr}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w700, color: Colors.white),
                              ),
                            ],
                          ),
                        );
                      }).toList(),

                      const SizedBox(height: 12),
                      Divider(color: Colors.white24, height: 1),

                      const SizedBox(height: 10),
                    
                        Row(
                        children: [
                          Expanded(
                            child: Text(
                             'subtotal'.tr,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w800,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          Text(
                               '${subtotal.toStringAsFixed(2)} ${'mru'.tr}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w900,
                                  color: Colors.white,
                                ),
                          ),   
                        ],
                      ),
                      const SizedBox(height: 6),
                    
                           Row(
                        children: [
                          Expanded(
                            child: Text(
                             'delivery_charge'.tr,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w800,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          Text(
                                '${deliveryAmount.toStringAsFixed(2)} ${'mru'.tr}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w900,
                                  color: Colors.white,
                                ),
                              ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'total'.tr,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w800,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          Obx(() => Text(
                                '${orderController.total.value.toStringAsFixed(2)} ${'mru'.tr}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w900,
                                  color: Colors.white,
                                ),
                              )),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 18),

                // --------- Bouton Commander ---------
                _PrimaryButton(
                  onPressed: readyToOrder
                      ? () async {
                          if (!(orderController.isLocationSelected.value &&
                              (orderController.deliveryChargeID.value != 0))) {
                            Get.snackbar('error'.tr, 'select_delivery_address'.tr,
                                snackPosition: SnackPosition.BOTTOM,
                                backgroundColor: Colors.black87,
                                colorText: Colors.white);
                            return;
                          }

                          orderController.isLoading.value = true;
                          try {
                            final success =
                                await orderController.createOrder(storeId: widget.store.id!);
                            if (success) {
                              orderController.clearOrder();
                              Get.snackbar('success'.tr, 'payment_success'.tr,
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white);
                              Get.offAll(() => ZoomDrawerWrapper(
                                    child: MyOrdersPage(),
                                    shouldOpenDrawer: false,
                                  ));
                            }
                          } finally {
                            orderController.isLoading.value = false;
                          }
                        }
                      : null,
                  child: Obx(() {
                    final loading = orderController.isLoading.value;
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (loading)
                          const SizedBox(
                              width: 18,
                              height: 18,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              )),
                        if (loading) const SizedBox(width: 10),
                        Text(
                          'order'.tr.isEmpty ? 'Order' : 'order'.tr,
                          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w800),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${orderController.total.value.toStringAsFixed(2)} ${'mru'.tr}',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    );
                  }),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _kv(String k, String v) {
    return Row(
      children: [
        Expanded(child: Text(k, style: const TextStyle(color: Colors.white70))),
        Text(v, style: const TextStyle(fontWeight: FontWeight.w700, color: Colors.white)),
      ],
    );
  }
}

/* ======================= Glass / Settings-like UI ======================= */

class _GlassScaffold extends StatelessWidget {
  final String title;
  final Widget child;
  final Color bgTop;
  final Color bgMid;

  const _GlassScaffold({
    required this.title,
    required this.child,
    required this.bgTop,
    required this.bgMid,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Dégradé de fond
        Positioned.fill(
          child: DecoratedBox(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [bgTop, bgMid, bgMid],
              ),
            ),
          ),
        ),

        // Contenu + header flouté
        SafeArea(
          top: true,
          bottom: false,
          child: Column(
            children: [
              const SizedBox(height: 8),
              // Header “verre dépoli”
              Padding(
                padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
                    child: Container(
                      height: 56,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.25),
                        border: Border.all(color: Colors.white.withOpacity(0.35)),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(width: 8),
                          _GlassIconButton(
                            icon: Icons.arrow_back_ios_new_rounded,
                            onTap: () => Get.back(),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              title,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontSize: 18,
                              ),
                            ),
                          ),
                          const SizedBox(width: 48), // équilibre visuel avec le bouton retour
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // Corps
              Expanded(child: child),
            ],
          ),
        ),
      ],
    );
  }
}

class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: const SizedBox(
            width: 40,
            height: 40,
            child: Icon(Icons.arrow_back_ios_new_rounded, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class _GlassSection extends StatelessWidget {
  final Widget child;
  const _GlassSection({required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.06),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white24),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.15), blurRadius: 18, offset: const Offset(0, 10))
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: child,
    );
  }
}

class _GlassTile extends StatelessWidget {
  final String title; // key i18n
  final Widget child;
  const _GlassTile({required this.title, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white24),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title.tr, style: const TextStyle(fontWeight: FontWeight.w700, color: Colors.white)),
          const SizedBox(height: 10),
          child,
        ],
      ),
    );
  }
}

class _SectionTitle extends StatelessWidget {
  final String title;
  const _SectionTitle({required this.title});

  @override
  Widget build(BuildContext context) {
    return Text(title.tr, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700, color: Colors.white));
  }
}

class _SectionSub extends StatelessWidget {
  final String textKey;
  const _SectionSub({required this.textKey});

  @override
  Widget build(BuildContext context) {
    return Text(textKey.tr, style: const TextStyle(color: Colors.white70));
  }
}





class _PrimaryButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;

  const _PrimaryButton({required this.onPressed, required this.child});

  factory _PrimaryButton.icon({required VoidCallback? onPressed, required Widget icon, required Widget label}) {
    return _PrimaryButton(
      onPressed: onPressed,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [icon, const SizedBox(width: 8), label],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withOpacity(0.18),
        foregroundColor: Colors.white,
        side: const BorderSide(color: Colors.white30),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(vertical: 12),
        elevation: 0,
      ),
      child: child,
    );
  }
}

class _GlassTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  const _GlassTextField({required this.controller, required this.hintText});

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      style: const TextStyle(color: Colors.white),
      cursorColor: Colors.white,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: const TextStyle(color: Colors.white60),
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        filled: true,
        fillColor: Colors.white.withOpacity(0.08),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white30),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.white),
        ),
      ),
    );
  }
}
