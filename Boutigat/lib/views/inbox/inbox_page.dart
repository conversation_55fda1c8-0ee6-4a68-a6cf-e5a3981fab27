import 'dart:async';
import 'dart:developer';
import 'dart:math' as math;

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/notifications_controller.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:get/get.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:boutigak/data/services/conversation_service.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/data/models/messages.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:boutigak/controllers/badge_controller.dart';

class InboxPage extends StatefulWidget {
  @override
  State<InboxPage> createState() => _InboxPageState();
}

class _InboxPageState extends State<InboxPage> {
  final InboxController inboxController = Get.put(InboxController());
  final AuthController authController = Get.put(AuthController());
  final BadgeController badgeController = Get.find<BadgeController>();

  @override
  void initState() {
    super.initState();
    // Optionnel: reset selon l’onglet par défaut
    if (inboxController.messagesSelected.value) {
      badgeController.resetBadge('messages');
    } else {
      badgeController.resetBadge('notifications');
    }
  }



  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return Scaffold(
      appBar: CustomAppBar(
        titleText: "inbox".tr,
        icon: FontAwesomeIcons.solidUser,
       
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Expanded(
                child: Obx(() => CustomTabButton(
                      text: 'messages'.tr,
                      isSelected: inboxController.messagesSelected.value,
                      onPressed: () {
                        inboxController.selectMessages();
                        badgeController.resetBadge('messages');
                      },
                      badgeCount: badgeController.getModuleCount('messages'),
                    )),
              ),
              Expanded(
                child: Obx(() => CustomTabButton(
                      text: 'notifications'.tr,
                      isSelected: inboxController.notificationsSelected.value,
                      onPressed: () {
                        inboxController.selectNotifications();
                        badgeController.resetBadge('notifications');
                      },
                      badgeCount: badgeController.getModuleCount('notifications'),
                    )),
              ),
            ],
          ),
          Expanded(
            child: Obx(() {
           
              return inboxController.messagesSelected.value
                  ? MessageList()
                  : NotificationList();
            }),
          ),
        ],
      ),
    );
  }
}



// _Shimmer(child: ...)
// _ShimmerBox(width:, height:, radius:)

class _ShimmerCircle extends StatelessWidget {
  final double size;
  const _ShimmerCircle({required this.size});

  @override
  Widget build(BuildContext context) {
    return _Shimmer(
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.grey.shade300, // ✅ couleur dans BoxDecoration
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

/// Shimmer qui IMITE une tuile de MESSAGE (avatar rond + 1 ligne + petit badge)
class ShimmerMessageTile extends StatelessWidget {
  const ShimmerMessageTile({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const _ShimmerCircle(size: 48), // avatar
              const SizedBox(width: 16),
              // Titre + sous-titre
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    // Titre (nom interlocuteur)
                    _ShimmerBox(width: double.infinity, height: 14, radius: 6),
                    SizedBox(height: 8),
                    // Sous-titre (dernier message)
                    _ShimmerBox(width: 220, height: 12, radius: 6),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              // Petit badge rond (ex: compteur non lus)
              const _ShimmerCircle(size: 22),
            ],
          ),
        ),
        // Divider aligné comme ta liste (indent = 72 = avatar 48 + spacing 16 + marge 8)
        const Divider(
          height: 1,
          thickness: 1,
          indent: 72,
          endIndent: 16,
          color: Color(0xFFE0E0E0),
        ),
      ],
    );
  }
}

/// Shimmer qui IMITE une tuile de NOTIFICATION (vignette carrée + 2 lignes + pastille)
class ShimmerNotificationTile extends StatelessWidget {
  const ShimmerNotificationTile({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // vignette 50x50 radius 10
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: _Shimmer(
              child: Container(
                width: 50,
                height: 50,
                // ✅ couleur uniquement via decoration
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Titre + message
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _ShimmerBox(width: double.infinity, height: 14, radius: 6),
                SizedBox(height: 8),
                _ShimmerBox(width: 260, height: 12, radius: 6),
              ],
            ),
          ),
          const SizedBox(width: 12),
          // pastille non-lu
          const _ShimmerCircle(size: 10),
        ],
      ),
    );
  }
}

class _ShimmerBox extends StatelessWidget {
  final double width;
  final double height;
  final double radius;
  const _ShimmerBox({
    required this.width,
    required this.height,
    this.radius = 8,
  });
  @override
  Widget build(BuildContext context) {
    return _Shimmer(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}

class _Shimmer extends StatefulWidget {
  final Widget child;
  const _Shimmer({required this.child});
  @override
  State<_Shimmer> createState() => _ShimmerState();
}

class _ShimmerState extends State<_Shimmer>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1400),
    )..repeat();
  }
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final double slide = _controller.value * 2 - 1; // -1 → 1
        return ShaderMask(
          shaderCallback: (rect) {
            return LinearGradient(
              begin: Alignment(-1 - slide, 0),
              end: Alignment(1 + slide, 0),
              colors: [
                Colors.grey.shade300,
                Colors.grey.shade100,
                Colors.grey.shade300,
              ],
              stops: const [0.1, 0.3, 0.4],
            ).createShader(rect);
          },
          blendMode: BlendMode.srcATop,
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

// ======================= Slivers prêts à l'emploi =======================

SliverList buildMessageShimmerSliver({int itemCount = 8}) {
  return SliverList(
    delegate: SliverChildBuilderDelegate(
      (context, i) => const ShimmerMessageTile(),
      childCount: itemCount,
    ),
  );
}

SliverList buildNotificationShimmerSliver({int itemCount = 10}) {
  return SliverList(
    delegate: SliverChildBuilderDelegate(
      (context, i) => const ShimmerNotificationTile(),
      childCount: itemCount,
    ),
  );
}
// ====== MessageList ======
class MessageList extends StatefulWidget {
  const MessageList({super.key});

  @override
  State<MessageList> createState() => _MessageListState();
}class _MessageListState extends State<MessageList> {
  final InboxController inboxController = Get.find();
  final BadgeController badgeController = Get.find<BadgeController>();

  // Pull-to-refresh custom
  bool _isRefreshing = false;
  double _pullExtent = 0;
  static const double _triggerDistance = 80.0;

  // --- Refresh helpers ---
  void _resetPull() => _pullExtent = 0;
  void _triggerRefresh() { _resetPull(); _refreshDiscussions(); }

  bool _onScrollNotification(ScrollNotification n) {
    if (n.metrics.axis != Axis.vertical) return false;

    final atTop = n.metrics.pixels <= n.metrics.minScrollExtent + 0.5;

    // 1) Au top et on tire vers le bas (Android/iOS)
    if (n is ScrollUpdateNotification) {
      final delta = n.scrollDelta ?? 0.0;
      if (atTop && delta < 0) {
        _pullExtent += -delta;
        if (!_isRefreshing && _pullExtent >= _triggerDistance) _triggerRefresh();
      }
    }

    // 2) Overscroll (bounce)
    if (n is OverscrollNotification && n.overscroll < 0) {
      _pullExtent += -n.overscroll;
      if (!_isRefreshing && _pullExtent >= _triggerDistance) _triggerRefresh();
    }

    // 3) Reset
    if (n is ScrollEndNotification || (n is ScrollUpdateNotification && !atTop)) {
      _resetPull();
    }
    return false;
  }

  Future<void> _refreshDiscussions() async {
    setState(() => _isRefreshing = true);
    try {
      await inboxController.refreshDiscussions();
      await badgeController.fetchBadgeCounts();
    } finally {
      if (mounted) setState(() => _isRefreshing = false);
    }
  }

  // --- Shimmer sliver (messages) ---
  SliverList _buildMessageShimmerSliver({int itemCount = 8}) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, i) => const ShimmerMessageTile(),
        childCount: itemCount,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: _onScrollNotification,
      child: Obx(() {
        // Tri par date du dernier message
        final sortedDiscussions = List.from(inboxController.discussions)
          ..sort((a, b) {
            final aTime = a['latest_message'] != null
                ? DateTime.parse(a['latest_message']['created_at'])
                : DateTime.parse(a['created_at']);
            final bTime = b['latest_message'] != null
                ? DateTime.parse(b['latest_message']['created_at'])
                : DateTime.parse(b['created_at']);
            return bTime.compareTo(aTime);
          });

        // 1) Chargement initial -> shimmer
        if (inboxController.isLoading.value && sortedDiscussions.isEmpty) {
          return CustomScrollView(
            physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
            slivers: [_buildMessageShimmerSliver(itemCount: 8)],
          );
        }

        // 2) Vide -> shimmer placeholder (une tuile)
       // 2) Vide (pas loading) -> état vide traduit
if (!inboxController.isLoading.value && sortedDiscussions.isEmpty) {
  return CustomScrollView(
    physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
    slivers: const [
      SliverFillRemaining(
        hasScrollBody: false,
        child: Center(
          child: SizedBox(width: 320, child: _EmptyMessagesView()),
        ),
      ),
    ],
  );
}


        // 3) Pendant refresh -> shimmer
        if (_isRefreshing) {
          return CustomScrollView(
            physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
            slivers: [_buildMessageShimmerSliver(itemCount: 8)],
          );
        }

        // 4) Liste réelle + shimmer de pagination
        return CustomScrollView(
          controller: inboxController.scrollController,
          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
          slivers: [
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  // Fin de liste -> pagination shimmer
                  if (index == sortedDiscussions.length) {
                    return inboxController.hasMoreDiscussions.value
                        ? const Padding(
                            padding: EdgeInsets.fromLTRB(0, 8, 0, 24),
                            child: Column(
                              children: [
                                ShimmerMessageTile(),
                                ShimmerMessageTile(),
                                ShimmerMessageTile(),
                              ],
                            ),
                          )
                        : const SizedBox.shrink();
                  }

                  final item = sortedDiscussions[index];
                  final connectedUserId = Get.find<AuthController>().user?.id;

                  final buyerID = item['buyer']?['id'];
                  final user = buyerID == connectedUserId ? item['seller'] : item['buyer'];
                  final int unreadCount = item['unread_count'] ?? 0;

                  return Column(
                    children: [
                      ListTile(
                        leading: cachedThumb(
  url: ((item['item'] as Map?)?['images'] as List?)?.isNotEmpty == true
      ? ((((item['item'] as Map?)?['images'] as List?)!.first as Map?)?['url'] as String?)
      : null,
  width: 48,
  height: 48,
  radius: 999, // cercle
  fit: BoxFit.cover,
),


                        title: Row(children: [
                          Text(' ${item['interlocutor']['name']}'),
                          const Spacer(),
                        ]),
                        subtitle: Text(
                          item['latest_message']?['is_an_offer'] == true
                              ? (item['latest_message']['sent_by_me'] == true
                                  ? 'You made an offer'
                                  : 'You received an offer')
                              : '${item['latest_message']?['content'] ?? ''}',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: unreadCount > 0 ? Colors.black : Colors.grey,
                            fontWeight: unreadCount > 0 ? FontWeight.w900 : FontWeight.w100,
                          ),
                        ),
                        trailing: unreadCount > 0
                            ? Container(
                                padding: const EdgeInsets.all(6),
                                decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                                child: Text(
                                  unreadCount.toString(),
                                  style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                                ),
                              )
                            : null,
                        onTap: () => inboxController.openConversation(
                          item, item['id'], '${user['firstname']} ${user['lastname']}', item['is_store_discussion'],
                        ),
                      ),
                      const Divider(height: 1, thickness: 1, indent: 72, endIndent: 16, color: Color(0xFFE0E0E0)),
                    ],
                  );
                },
                childCount: sortedDiscussions.length + (inboxController.hasMoreDiscussions.value ? 1 : 0),
              ),
            ),
          ],
        );
      }),
    );
  }
}


// ====== NotificationList ======
class NotificationList extends StatefulWidget {
  const NotificationList({Key? key}) : super(key: key);

  @override
  State<NotificationList> createState() => _NotificationListState();
}

class _NotificationListState extends State<NotificationList> {
  final NotificationController notificationController = Get.put(NotificationController());
  final BadgeController badgeController = Get.find<BadgeController>();
  final ScrollController scrollController = ScrollController();

  // Pull-to-refresh custom
  bool _isRefreshing = false;
  double _pullExtent = 0;
  static const double _triggerDistance = 80.0;

  @override
  void initState() {
    super.initState();
    notificationController.fetchNotifications();
    badgeController.resetBadge('notifications');
    scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.dispose();
  }

  // Pagination
  void _scrollListener() {
    if (scrollController.position.pixels >=
            scrollController.position.maxScrollExtent - 200 &&
        !notificationController.isLoading.value &&
        notificationController.currentPage.value * 15 <
            notificationController.totalNotifications.value) {
      notificationController.loadMoreNotifications();
    }
  }

  // Pull detector
  void _resetPull() => _pullExtent = 0;
  void _triggerRefresh() { _resetPull(); _refreshNotifications(); }

  bool _onScrollNotification(ScrollNotification n) {
    if (n.metrics.axis != Axis.vertical) return false;

    final atTop = n.metrics.pixels <= n.metrics.minScrollExtent + 0.5;

    // 1) Au top et on tire vers le bas (Android/iOS)
    if (n is ScrollUpdateNotification) {
      final delta = n.scrollDelta ?? 0.0;
      if (atTop && delta < 0) {
        _pullExtent += -delta;
        if (!_isRefreshing && _pullExtent >= _triggerDistance) _triggerRefresh();
      }
    }
    // 2) Overscroll (bounce)
    if (n is OverscrollNotification && n.overscroll < 0) {
      _pullExtent += -n.overscroll;
      if (!_isRefreshing && _pullExtent >= _triggerDistance) _triggerRefresh();
    }
    // 3) Reset
    if (n is ScrollEndNotification || (n is ScrollUpdateNotification && !atTop)) {
      _resetPull();
    }
    return false;
  }

  Future<void> _refreshNotifications() async {
    setState(() => _isRefreshing = true);
    try {
      await notificationController.fetchNotifications(page: 1);
    } finally {
      if (mounted) setState(() => _isRefreshing = false);
    }
  }

  // Sliver shimmer (notifications)
  SliverList _buildNotificationShimmerSliver({int itemCount = 10}) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, i) => const ShimmerNotificationTile(),
        childCount: itemCount,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: _onScrollNotification,
      child: Obx(() {
        // 1) Chargement initial -> shimmer
        if (notificationController.isLoading.isTrue &&
            notificationController.notifications.isEmpty) {
          return CustomScrollView(
            physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
            slivers: [_buildNotificationShimmerSliver(itemCount: 10)],
          );
        }

        // 2) Erreur initiale -> shimmer doux (même squelette)
        if (notificationController.isError.isTrue &&
            notificationController.notifications.isEmpty) {
          return CustomScrollView(
            physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
            slivers: [_buildNotificationShimmerSliver(itemCount: 6)],
          );
        }

        // 3) Vide -> placeholder shimmer (une tuile)
       // 3) Vide (pas loading) -> état vide traduit
if (!notificationController.isLoading.value &&
    notificationController.notifications.isEmpty) {
  return CustomScrollView(
    physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
    slivers: const [
      SliverFillRemaining(
        hasScrollBody: false,
        child: Center(
          child: SizedBox(width: 320, child: _EmptyNotificationsView()),
        ),
      ),
    ],
  );
}


        // 4) Pendant refresh -> shimmer
        if (_isRefreshing) {
          return CustomScrollView(
            physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
            slivers: [_buildNotificationShimmerSliver(itemCount: 10)],
          );
        }

        // 5) Liste réelle + shimmer de pagination
        return CustomScrollView(
          controller: scrollController,
          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
          slivers: [
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < notificationController.notifications.length) {
                    final n = notificationController.notifications[index];
                    return ListTile(
                      leading: cachedThumb(
  url: n.image,
  width: 50,
  height: 50,
  radius: 10,
  fit: BoxFit.cover,
),

                      title: Text(n.title, style: const TextStyle(fontWeight: FontWeight.w700)),
                      subtitle: Text(n.message),
                    );
                  } else {
                    // pagination -> shimmers
                    return notificationController.isLoading.isTrue
                        ? const Padding(
                            padding: EdgeInsets.only(bottom: 16),
                            child: Column(
                              children: [
                                ShimmerNotificationTile(),
                                ShimmerNotificationTile(),
                                ShimmerNotificationTile(),
                              ],
                            ),
                          )
                        : const SizedBox.shrink();
                  }
                },
                childCount: notificationController.notifications.length + 1,
              ),
            ),
          ],
        );
      }),
    );
  }
}


// ====== Optional: NotificationInfiniteScrollList (sans loaders)
class NotificationListView extends StatelessWidget {
  final NotificationController notificationController;
  const NotificationListView({super.key, required this.notificationController});
  @override
  Widget build(BuildContext context) => NotificationInfiniteScrollList(notificationController: notificationController);
}

class NotificationInfiniteScrollList extends StatelessWidget {
  final NotificationController notificationController;
  final BadgeController badgeController = Get.find<BadgeController>();
  final ScrollController scrollController = ScrollController();

  NotificationInfiniteScrollList({super.key, required this.notificationController}) {
    scrollController.addListener(() {
      if (scrollController.position.pixels == scrollController.position.maxScrollExtent &&
          !notificationController.isLoading.value) {
        notificationController.loadMoreNotifications();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return ListView.builder(
        controller: scrollController,
        itemCount: notificationController.notifications.length + 1,
        itemBuilder: (context, index) {
          if (index < notificationController.notifications.length) {
            final n = notificationController.notifications[index];
            return ListTile(
             leading: cachedThumb(
  url: (n.storeImageUrls.isNotEmpty ? n.storeImageUrls.first : null),
  width: 50,
  height: 50,
  radius: 10,
  fit: BoxFit.cover,
),

              title: Text(n.title, style: const TextStyle(fontWeight: FontWeight.w700)),
              subtitle: Text(n.message),
              trailing: n.isRead ? null : const Icon(Icons.fiber_manual_record, color: Colors.red),
              onTap: () => notificationController.markAsRead(n.id),
            );
          } else {
            return notificationController.isLoading.isTrue
                ? const Padding(
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                    child: Column(children: [
                     ShimmerNotificationTile(),
                      ShimmerNotificationTile(),
                      ShimmerNotificationTile(),
                    ]),
                  )
                : const SizedBox.shrink();
          }
        },
      );
    });
  }
}


class _EmptyMessagesView extends StatelessWidget {
  const _EmptyMessagesView();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          'no_messages'.tr, // <- traduction
          style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Text(
          'pull_down_to_refresh'.tr, // <- traduction
          style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
Widget cachedThumb({
  required String? url,
  required double width,
  required double height,
  double radius = 8,
  BoxFit fit = BoxFit.cover,
}) {
  if (url != null && url.isNotEmpty) {
    return CachedImageWidget(
      imageUrl: url,
      width: width,
      height: height,
      fit: fit,
      borderRadius: BorderRadius.circular(radius),
    );
  }
  // Fallback asset si pas d’URL
  return ClipRRect(
    borderRadius: BorderRadius.circular(radius),
    child: Image.asset(
      'assets/images/icon.png',
      width: width,
      height: height,
      fit: BoxFit.cover,
    ),
  );
}

class _EmptyNotificationsView extends StatelessWidget {
  const _EmptyNotificationsView();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.notifications_none, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          'no_notifications'.tr, // <- traduction
          style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Text(
          'no_notifications_hint'.tr, // <- traduction
          style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class CustomTabButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onPressed;
  final int badgeCount;

  const CustomTabButton({
    required this.text,
    required this.isSelected,
    required this.onPressed,
    this.badgeCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Barre du haut

          Container(
            height: 1,
            color: !isSelected ? Colors.grey[300] : AppColors.primary,
            width: double.infinity,
          ),

          Container(
            height: 44,
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primary : Colors.transparent,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  text,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                if (badgeCount > 0)
                  Container(
                    margin: EdgeInsets.only(left: 8),
                    padding: EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      badgeCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Barre du bas
          Container(
            height: 1,
            color: !isSelected ? Colors.grey[300] : AppColors.primary,
            width: double.infinity,
          ),
        ],
      ),
    );
  }
}

class ConversationPage extends StatefulWidget {
  final int itemId;
  final dynamic discussionId;
  final Map<String, dynamic>? item;
  final Map<String, dynamic>? user;
  final String interlocutor;
  final bool isStoreDiscussion;
  final String? phoneNumber;

  const ConversationPage({
    Key? key,
    this.user,
    required this.itemId,
    required this.item,                       
    required this.discussionId,
    required this.interlocutor,
    this.isStoreDiscussion = false,
    this.phoneNumber,
  }) : super(key: key);

  @override
  State<ConversationPage> createState() => _ConversationPageState();
}

class _ConversationPageState extends State<ConversationPage> {
  final ConversationController conversationController =
      Get.put(ConversationController());
  final TextEditingController offerController = TextEditingController();
  final ScrollController scrollController = ScrollController();



  @override
  void initState() {
    super.initState();

    // Load discussion details when the page loads
    _loadInitialData();

    // Listen to message changes to auto-scroll when new messages arrive
    ever(conversationController.messages, (_) {
      // Only scroll if we're near the bottom or if it's a new message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_shouldAutoScroll()) {
          jumpToBottom();
        }
      });
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  // Load initial data and scroll to bottom
  Future<void> _loadInitialData() async {
    // Set the other user ID for blocking functionality
    final item = widget.item;
    log('item_in_convo  $item');

    final connectedUserId = Get.find<AuthController>().user?.id;

    // Get the other user ID from the item's user field or from widget.user
    int? otherUserId =  item!['user']['id'];

    // if (widget.user != null && widget.user!['id'] != null) {

    //   log('in condition 1');

    //   log('widget other ${widget.user}');
    //   otherUserId = widget.user!['id'];
    // } else if (item != null && item['user'] != null && item['user']['id'] != null) {
    //   log('in condition 2');
    //   // If the item has a user field, use that user's ID
    //   otherUserId = item['user']['id'];
    // }

    log('connected user id: $connectedUserId');
    log('other user id: $otherUserId');

    if (otherUserId != null) {
      await conversationController.setOtherUser(otherUserId);
    }

    await conversationController.loadDiscussionDetails(widget.discussionId);
    // Scroll to bottom immediately after loading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      jumpToBottom();
    });
  }

  // Check if we should auto-scroll (only if user is near the bottom)
  bool _shouldAutoScroll() {
    if (!scrollController.hasClients) return false;

    final position = scrollController.position;
    // Auto-scroll if user is within 100 pixels of the bottom
    return (position.maxScrollExtent - position.pixels) < 100;
  }

  // Simple jump scroll to bottom (no animation)
  void _scrollToBottomSmooth() {
    jumpToBottom();
  }

  void scrollToBottom({bool animated = false}) {
    if (animated) {
      _scrollToBottomSmooth();
    } else {
      jumpToBottom();
    }
  }

  // Instant scroll to bottom (no animation, no delays)
  void jumpToBottom() {
    if (scrollController.hasClients) {
      try {
        scrollController.jumpTo(scrollController.position.maxScrollExtent);
      } catch (e) {
        // Handle any potential scroll errors silently
      }
    }
  }

  // Function to make a phone call
  void _makePhoneCall(String? phoneNumber) async {

    log('phone number ${phoneNumber}');
    if (phoneNumber == null || phoneNumber.isEmpty) {
      Get.snackbar(
        'Error',
        'Phone number not available',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final Uri url = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      Get.snackbar(
        'Error',
        'Could not launch phone call',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Get the appropriate phone number based on discussion type
  String? _getPhoneNumber() {
    log('item ${widget.item}');
    log('user ${widget.user}');
    log('phoneNumber parameter: ${widget.phoneNumber}');

    // First, try to use the phone number passed from the discussion list
    if (widget.phoneNumber != null && widget.phoneNumber!.isNotEmpty) {
      log('Using passed phone number: ${widget.phoneNumber}');
      return widget.phoneNumber;
    }

    // Fallback to the old logic if no phone number was passed
    if (widget.isStoreDiscussion) {
      // For store discussions, get the connected user's phone (the buyer)
      final connectedUserId = Get.find<AuthController>().user?.id;

      if (widget.item != null && widget.item!['user_id'] == connectedUserId) {
        log('connected user ${widget.user?['phone']}');
        return widget.user?['phone'];
      } else {
        return widget.item?['user']?['phone'];
      }
    } else {
      // For normal item discussions, get the other user's phone
      return widget.user?['phone'];
    }
  }

  // Handle sending messages
  void _handleSendMessage() {
    if (conversationController.isMakingOffer.value) {
      final offerValue =
          double.tryParse(conversationController.newMessage.value) ?? 0.0;
      if (offerValue > 0) {
        conversationController.offerAmount.value = offerValue;
        conversationController.addOfferMessage(
          widget.discussionId,
          offerValue,
        );
        conversationController.textController.clear();
        conversationController.isMakingOffer.value = false;
        FocusScope.of(context).unfocus();

        // Simple jump to bottom when sending
        WidgetsBinding.instance.addPostFrameCallback((_) {
          jumpToBottom();
        });
      }
    } else {
      conversationController.sendMessage(
          widget.discussionId, false, null, widget.isStoreDiscussion);

      // Simple jump to bottom when sending
      WidgetsBinding.instance.addPostFrameCallback((_) {
        jumpToBottom();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    String imgurl =
        widget.item != null && widget.item!['images'][0]['value'] != null
            ? '${widget.item!['images'][0]['value']}'
            : '${widget.item!['images'][0]['url']}';

    bool userIsTheOwner = widget.item != null &&
        widget.item!['user_id'] == Get.find<AuthController>().user?.id;

    final InboxController inboxController = Get.find();

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (didPop) {
          // This will be called when the user navigates back
          // Either by pressing back button or swiping back on iOS/Android
          conversationController.clearMessages();
          inboxController.fetchDiscussions();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              conversationController.clearMessages();
              inboxController.fetchDiscussions();
              Get.back();
            },
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  widget.interlocutor,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.phone, color: AppColors.primary, size: 20),
                    onPressed: () => _makePhoneCall(_getPhoneNumber()),
                  ),
                  Obx(() => IconButton(
                    icon: Icon(
                      conversationController.isUserBlocked.value
                        ? Icons.block
                        : Icons.block_outlined,
                      color: conversationController.isUserBlocked.value
                        ? Colors.red
                        : Colors.blue,
                      size: 30,
                    ),
                    onPressed: () => conversationController.toggleBlockUser(),
                  )),
                ],
              ),
            ],
          ),
        ),
        body: Column(
          children: [
            // Item details section
            if (widget.item != null)
              Container(
                padding: const EdgeInsets.all(16),
                color: Theme.of(context).dividerColor,
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        imgurl,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 50,
                            height: 50,
                            color: Colors.grey[300],
                            child: const Icon(Icons.image_not_supported),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.item!['title'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text("${widget.item!['price']} mru"),
                        ],
                      ),
                    ),
                  
                  ],
                ),
              ),

            // Messages list
            Expanded(
              child: Obx(() {
                if (conversationController.isloading.value &&
                    conversationController.messages.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CupertinoActivityIndicator(radius: 20),
                        SizedBox(height: 16),
                        Text(
                          'Loading messages...',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: scrollController,
                  physics: const BouncingScrollPhysics(),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: conversationController.messages.length +
                      (conversationController.isloading.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    // Loading indicator at bottom
                    if (index == conversationController.messages.length) {
                      return conversationController.isloading.value
                          ? const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(
                                child: CupertinoActivityIndicator(radius: 15),
                              ),
                            )
                          : const SizedBox.shrink();
                    }

                    final message = conversationController.messages[index];
                    final dateCategory = message.dateCategory ?? '';

                    bool showDateHeader = index == 0 ||
                        (message.dateCategory !=
                            conversationController
                                .messages[index - 1].dateCategory);

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Date header
                        if (showDateHeader)
                          Center(
                            child: Container(
                              margin: const EdgeInsets.symmetric(vertical: 8.0),
                              padding: const EdgeInsets.symmetric(
                                  vertical: 6.0, horizontal: 16.0),
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                dateCategory,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),

                        // Message bubble
                     
                            
                            MessageBubble(message: message)
                      ],
                    );
                  },
                );
              }),
            ),

            // Message input section
            Obx(() {
              final isKeyboardOpen =
                  MediaQuery.of(context).viewInsets.bottom > 0;
              return Container(
                padding: EdgeInsets.only(
                  left: 16.0,
                  right: 8.0,
                  top: 8.0,
                  bottom: isKeyboardOpen ? 8.0 : 34.0,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border(
                    top: BorderSide(
                      color: Colors.grey[300]!,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 150.0,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: TextField(
                            controller: conversationController.textController,
                            maxLines: 4,
                            minLines: 1,
                            keyboardType:
                                conversationController.isMakingOffer.value
                                    ? TextInputType.number
                                    : TextInputType.text,
                            onChanged: (value) {
                              conversationController.newMessage.value = value;
                            },
                            decoration: InputDecoration(
                              hintText:
                                  conversationController.isMakingOffer.value
                                      ? 'Enter your offer amount'
                                      : 'Type a message...',
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 16),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.send, color: Colors.white),
                        onPressed: _handleSendMessage,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

class MessageBubble extends StatelessWidget {
  final Message message;

  MessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          message.sentByMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Align(
          alignment:
              message.sentByMe ? Alignment.centerRight : Alignment.centerLeft,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width *
                  0.75, // Limite la largeur de la bulle à 75% de la largeur de l'écran
            ),
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 14),
              decoration: BoxDecoration(
                color:
                    message.sentByMe ? AppColors.primary : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                message.content ?? '',
                style: TextStyle(
                  color: message.sentByMe ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            message.formattedTime,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 10,
            ),
          ),
        ),
      ],
    );
  }
}
