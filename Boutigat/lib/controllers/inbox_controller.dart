import 'dart:convert';
import 'dart:developer';

import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/models/messages.dart';
import 'package:boutigak/data/services/conversation_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/data/services/blocking_service.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class InboxController extends GetxController {
  var messagesSelected = true.obs;
  var notificationsSelected = false.obs;
  var discussions = <dynamic>[].obs;
  var isLoading = false.obs;
  var currentPage = 1.obs;
  var lastPage = 1.obs;
  var totalDiscussions = 0.obs;
  var hasMoreDiscussions = true.obs;
  final ScrollController scrollController = ScrollController();

  AuthController authController = Get.find<AuthController>();

  @override
  void onInit() {
    if (authController.isAuthenticated.value) {
      // Fetch discussions when accessing the page
      fetchDiscussions();
    }

    // Setup scroll controller for infinite scrolling
    scrollController.addListener(_scrollListener);

    super.onInit();
  }

  @override
  void onClose() {
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.onClose();
  }

  void _scrollListener() {
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 &&
        !isLoading.value &&
        hasMoreDiscussions.value) {
      loadMoreDiscussions();
    }
  }

  void selectMessages() {
    messagesSelected.value = true;
    notificationsSelected.value = false;
  }

  void selectNotifications() {
    messagesSelected.value = false;
    notificationsSelected.value = true;
  }

  Future<void> markMessagesAsRead(int discussionId) async {
    try {
      await WebService.post(
        'api/discussions/$discussionId/mark-read',
      );
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  void openConversation(Map<String, dynamic> item, int discussionId, interlocutor, isStoreDiscussion) async {
    final connectedUserId = Get.find<AuthController>().user?.id;
    final user = item['buyer_id'] == connectedUserId ? item['seller'] : item['buyer'];

    // Mark messages as read before opening the conversation
    await markMessagesAsRead(discussionId);

    // If it's a store discussion, use store name as interlocutor
    final String displayInterlocutor = isStoreDiscussion && item['store'] != null
        ? item['store']['name']
        : interlocutor;

    // Extract phone number from the interlocutor data
    String? phoneNumber;
    if (item['interlocutor'] != null && item['interlocutor']['phone'] != null) {
      phoneNumber = item['interlocutor']['phone'];
    } else if (isStoreDiscussion && item['store'] != null && item['store']['owner_phone'] != null) {
      // For store discussions, use store owner's phone
      phoneNumber = item['store']['owner_phone'];
    } else if (user != null && user['phone'] != null) {
      // Fallback to user phone
      phoneNumber = user['phone'];
    }

    Get.to(() => ConversationPage(
      itemId: item['item']['id'],
      item: item['item'],
      discussionId: discussionId,
      interlocutor: item['interlocutor']['name'],
      isStoreDiscussion: isStoreDiscussion,
      user: user,
      phoneNumber: phoneNumber,
    ));
  }

  Future<void> fetchDiscussions() async {
    print('fetchDiscussions called');
    isLoading.value = true;
    currentPage.value = 1;

    try {
      Map<String, dynamic>? response = await ConversationService.getDiscussions(page: currentPage.value);

      log('response fetch discussion ${response}');

      if (response != null) {
        List<dynamic> fetchedDiscussions = response['data'] as List<dynamic>;

        log('fetched discussions api ${fetchedDiscussions}');
        discussions.value = fetchedDiscussions;

        log('fetched discussions ${discussions.value}');



        
        var pagination = response['pagination'];
        currentPage.value = pagination['current_page'];
        lastPage.value = pagination['last_page'];
        totalDiscussions.value = pagination['total'];
        hasMoreDiscussions.value = currentPage.value < lastPage.value;




        log('before loading ');
        
        isLoading.value = false;
      } else {
        // Handle null response
        discussions.value = [];
        hasMoreDiscussions.value = false;
      }
    } catch (e) {
      log("Error fetching discussions: $e");
      // Reset state on error
      discussions.value = [];
      hasMoreDiscussions.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadMoreDiscussions() async {
    if (isLoading.value || !hasMoreDiscussions.value) return;

    isLoading.value = true;
    try {
      Map<String, dynamic>? response = await ConversationService.getDiscussions(
        page: currentPage.value + 1,
      );

      if (response != null) {
        List<dynamic> newDiscussions = response['data'] as List<dynamic>;
        discussions.addAll(newDiscussions);

        // Update pagination info
        var pagination = response['pagination'];
        currentPage.value = pagination['current_page'];
        lastPage.value = pagination['last_page'];
        totalDiscussions.value = pagination['total'];
        hasMoreDiscussions.value = currentPage.value < lastPage.value;
      }
    } catch (e) {
      log("Error loading more discussions: $e");
      // Don't reset state on pagination error, just stop loading
    } finally {
      isLoading.value = false;
    }
  }

  // Method to refresh discussions (called by pull-to-refresh)
  Future<void> refreshDiscussions() async {
    // Reset pagination state
    currentPage.value = 1;
    hasMoreDiscussions.value = true;

    // Fetch fresh data
    await fetchDiscussions();
  }

  // Method to clear discussions when user exits
  void clearDiscussions() {
    discussions.clear();
    currentPage.value = 1;
    hasMoreDiscussions.value = true;
  }

}




class ConversationController extends GetxController {
  var messages = <Message>[].obs;
  var newMessage = ''.obs;
  TextEditingController textController = TextEditingController();
  var isMakingOffer = false.obs;
  var offerAmount = 0.0.obs;
  var isloading = false.obs;
  var currentDiscussionId = 0.obs;
  var isUserBlocked = false.obs;
  var isBlockedByUser = false.obs;
  var otherUserId = 0.obs;

  Future<dynamic> createDiscussion(int itemId) async {
    dynamic success = await ConversationService.createDiscussion(itemId);

    return success;
}


Future<dynamic> createStoreDiscussion(int itemId , int storeId) async {
  try {
    final response = await WebService.post(
      'api/discussions',
      body: {
        'item_id': itemId,
        'store_id': storeId,
        'is_store_discussion': true
      },
    );



    log('responbse store discussion ${response.body} ${response.statusCode}'  );



    if (response.statusCode == 200 || response.statusCode == 201) {
      return

        jsonDecode(response.body);
    }
    return null;
  } catch (e) {
    print('Error creating store discussion: $e');
    return null;
  }
}
  void sendMessage(int discussionId, bool isOffer, double? price, bool isStoreDiscussion) async {
    if (newMessage.value.isNotEmpty || isOffer) {
      final messageContent = isOffer ? 'Offer: ${price ?? 0}' : newMessage.value;


      // Create optimistic message
      final optimisticMessage = Message(
        content: messageContent,
        sentByMe: true,
        timestamp: DateTime.now(),
        isOffer: isOffer,
        price: price,
        dateCategory: _getDateCategory(DateTime.now()),
      );

      // Add message optimistically to UI
      messages.add(optimisticMessage);

      // Clear input fields immediately
      final originalMessage = newMessage.value;
      newMessage.value = '';
      textController.clear();

      // Send message to API
      bool success = await ConversationService.sendMessage(discussionId, originalMessage, isOffer, price, isStoreDiscussion);

      log('send message status ${success}');
      if (!success) {
        // Remove optimistic message and show retry option
        messages.removeLast();

        // Show retry dialog
        Get.dialog(
          AlertDialog(
            title: Text('Message Failed'),
            content: Text('Failed to send message. Would you like to retry?'),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  // Restore message and retry
                  newMessage.value = originalMessage;
                  textController.text = originalMessage;
                  sendMessage(discussionId, isOffer, price, isStoreDiscussion);
                },
                child: Text('Retry'),
              ),
            ],
          ),
        );
      } else {
        // Refresh to get the actual message from server (with proper ID, etc.)
        await loadDiscussionDetails(discussionId);
      }
    }
  }

  String _getDateCategory(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(date.year, date.month, date.day);

    if (messageDate == today) {
      return 'Today';
    } else if (messageDate == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('MMM dd, yyyy').format(date);
    }
  }

  void addOfferMessage(int discussionID , double offerAmount) async {
    if (offerAmount <= 0) {
     // // Get.snakbar("Invalid Offer", "Please enter a valid offer amount.");
      return;
    }
    bool success = await ConversationService.sendMessage(discussionID ,'', true , offerAmount , false);
    if (success) {

      // print("offer ");
      messages.add( Message(
        content: '$offerAmount',
        price: offerAmount,
        sentByMe: true,
        timestamp: DateTime.now(),
        isOffer: true,
      ));
    }
  }

  void receiveMessage(String content) {
    messages.add(Message(
      content: content,
      sentByMe: false,
      timestamp: DateTime.now(),
    ));
  }

  // Call this to load messages for a discussion
  Future<void> loadDiscussionDetails(int discussionId) async {
    isloading.value = true;
    currentDiscussionId.value = discussionId;
    try {
      // Fetch messages from API/service
      final discussionDetails = await ConversationService.getDiscussionDetails(discussionId);

      // Extract messages and blocking status
      final messagesList = discussionDetails['messages'] as List<Message>;
      final blockingStatus = discussionDetails['blocking_status'] as Map<String, dynamic>;

      messages.assignAll(messagesList);

      // Update blocking status
      isUserBlocked.value = blockingStatus['is_blocked'] ?? false;
      isBlockedByUser.value = blockingStatus['is_blocked_by'] ?? false;

    } catch (e) {
      log("Error loading discussion details: $e");
    } finally {
      isloading.value = false;
    }
  }

  // This is called from main.dart when a notification arrives
  Future<void> refreshMessagesIfInConversation(int discussionId) async {
    if (currentDiscussionId.value == discussionId) {
      await loadDiscussionDetails(discussionId);
    }
  }

  // Method to clear messages when user exits conversation
  void clearMessages() {
    messages.clear();
    currentDiscussionId.value = 0;
    newMessage.value = '';
    textController.clear();
    isMakingOffer.value = false;
    offerAmount.value = 0.0;
    isUserBlocked.value = false;
    isBlockedByUser.value = false;
    otherUserId.value = 0;
  }

  // Method to set other user ID and check block status
  Future<void> setOtherUser(int userId) async {
    otherUserId.value = userId;
    await checkBlockStatus();
  }

  // Method to check if user is blocked
  Future<void> checkBlockStatus() async {
    if (otherUserId.value == 0) return;

    try {
      final response = await BlockingService.checkBlockStatus(otherUserId.value);
      if (response != null) {
        isUserBlocked.value = response['is_blocked'] ?? false;
      }
    } catch (e) {
      log("Error checking block status: $e");
    }
  }

  // Method to toggle block/unblock user
  Future<void> toggleBlockUser() async {
    if (otherUserId.value == 0) return;

    try {
      Map<String, dynamic>? response;

      if (isUserBlocked.value) {
        // Unblock user
        response = await BlockingService.unblockUser(otherUserId.value);
        if (response != null) {
          isUserBlocked.value = false;
          
          Get.snackbar(
            'Success',
            'User unblocked successfully',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

        }
        
      } else {
        // Block user
        response = await BlockingService.blockUser(otherUserId.value);
        if (response != null) {
          isUserBlocked.value = true;
          Get.snackbar(
            'Success',
            'User blocked successfully',
            backgroundColor: Colors.orange,
            colorText: Colors.white,
          );
        }
      }

      if (response == null) {
        Get.snackbar(
          'Error',
          'Failed to ${isUserBlocked.value ? 'unblock' : 'block'} user',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      log("Error toggling block status: $e");
      Get.snackbar(
        'Error',
        'An error occurred',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
